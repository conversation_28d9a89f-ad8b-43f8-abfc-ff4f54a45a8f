<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金-add')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-margin-add" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
        <input type="hidden" th:value="${customerId}" name="customerId">
        <!--保证金状态-新建-->
        <input type="hidden" th:value="${marginVbillstatus}" name="marginVbillstatus">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span
                                            style="color: red; ">保证金金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="marginAmount" id="marginAmount"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                               class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款类型：</label>
                                    <div class="col-sm-8">
                                        <select name="marginType" class="form-control valid" required
                                                th:with="type=${@dict.getType('margin_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" class="form-control valid" required
                                                th:with="type=${@dict.getType('pay_method')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">类型：</label>
                                    <div class="col-sm-8">
                                        <select name="logisticsType" class="form-control valid" required
                                                th:with="type=${@dict.getType('logistics_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>对应合同号：</span></label>
                                    <div class="col-sm-8">
                                        <input name="contractNo" id="contractNo" class="form-control" type="text" maxlength="50">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限开始：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control" id="businessPeriodStart" name="businessPeriodStart"
                                               placeholder="业务期限开始"  readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限结束：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control" id="businessPeriodEnd" name="businessPeriodEnd"
                                               placeholder="业务期限结束"  readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><font color="red">结算公司：</font></label>
                                    <div class="col-sm-8">
<!--                                        <input name="xx" id="xx" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" class="form-control" required type="text" maxlength="20">-->
                                        <select name="balaCorp" id="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required disabled>
                                            <option value="">---请选择结算公司---</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:selected="${client.balaCorp == dict.dictValue}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-6">
                                            <textarea name="memo" maxlength="500" class="form-control valid" rows="3" ></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!--证件上传 start-->
        <div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapsePic">证件上传</a>
                    </h5>
                </div>
                <div id="collapsePic" class="panel-collapse collapse in">
                    <div class="panel-body" id="picType">
                        <div class="row" th:each="dict : ${marginPicList}" >
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-9" th:text="${dict.context}+'：'"></label>
                                </div>
                            </div>
                            <div class="col-md-9 col-sm-6">
                                <div class="form-group">
                                    <div class="col-sm-7">
                                        <input th:id="'image'+${dict.value}" class="form-control"
                                               th:name="'image'+${dict.value}" type="file" multiple>
                                        <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}" type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--证件上传 end-->

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">合同信息</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 10%;">合同文件</th>
                                <th style="width: 10%;">合同名称</th>
                                <th style="width: 12%;">合同开始时间</th>
                                <th style="width: 10%;">合同到期时间</th>
                                <th style="width: 10%;">合同预警时间</th>
                            </tr>
                            </thead>
                            <tbody>


                            <tr th:each="mapS,status:${contracts}">
                                <th:block th:if='${mapS.tid!=null}'>
                                    <td>
                                        <a href="#" th:name="${mapS.filePath}" onclick="downloadFile(this.name)">[[${mapS.fileName}]]</a>
                                        <input type="hidden" th:name="|contractList[${status.index}].tid|"  th:value="${mapS.tid}">
                                        <input  th:name="|contractList[${status.index}].fileName|" type="hidden" th:value="${mapS.fileName}">
                                    </td>
                                </th:block>
                                <th:block th:if='${mapS.tid==null}'>
                                    <td></td>
                                </th:block>
                                <td th:text="${mapS.name}"></td>
                                <td th:text="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}"></td>
                                <td th:text="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}"></td>
                                <td th:text="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}"></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";
    //保证金附件List
    var marginPicList = [[${marginPicList}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapsePic').collapse('show');
        $('#collapseThree').collapse('show');

        for (var i = 0; i < marginPicList.length; i++) {
            var dictValue = marginPicList[i].value;
            var publishFlag = "cmt_" + dictValue;
            var fileType = null;
            if(dictValue == 0){
                //文件类型
                fileType = 'file';
            }
            var picParam = {
                maxFileCount: 0,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: fileType//文件类型
            };
            var tid = "tid" + dictValue;
            var imageId = "image" + dictValue;
            $.file.initAddFiles(imageId, tid, picParam);
        }
    });


    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.loading("正在处理中，请稍后...");
            $("#image0").fileinput('upload');
            jQuery.subscribe("cmt_0",cmtFile_1);
            jQuery.subscribe("cmt_1",commit);
        }
    }

    function cmtFile_1(){
        $("#image1").fileinput('upload');
    }

    function commit() {
        // console.log($('#form-margin-add').serialize())
        // return
        $('#balaCorp').attr("disabled",false);
        $.operate.saveTab(prefix + "/add", $('#form-margin-add').serialize());
    }

</script>
</body>

</html>