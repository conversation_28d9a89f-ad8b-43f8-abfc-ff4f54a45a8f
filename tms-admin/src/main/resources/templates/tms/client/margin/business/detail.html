<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金管理-列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <input type="hidden" name="customerId" th:value="${customerId}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">年份：</label>
                            <div class="col-sm-8">
                                <input name="year" id="year"  placeholder="请输入年份" class="form-control valid" type="text"
                                       aria-required="true" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">月份：</label>
                            <div class="col-sm-8">
                                <input name="month" id="month" placeholder="请输入月份" class="form-control valid" type="text"
                                       aria-required="true"  autocomplete="off">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: prefix + "/business/detail_list",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            columns: [
                {
                    title: '单据状态',
                    field: 'marginVbillstatus',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '新建';
                        }else if(value == 1){
                            return '已申请';
                        }else if(value == 2){
                            return '审核中';
                        }else if(value == 3){
                            return '已收款';
                        }else if(value == 4){
                            return '已付款';
                        }
                    }
                },
                {
                    title: '收支类型',
                    field: 'marginType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '<span class="label label-warning">付款</span>';
                        }else if(value == 1){
                            return '<span class="label label-info">收款</span>';
                        }
                    }
                },
                {
                    title: '申请人',
                    field: 'applyUserName',
                    align: 'left'
                },
                {
                    title: '申请时间',
                    field: 'applyDate',
                    align: 'left'
                },
                {
                    title: '收支时间',
                    field: 'payDate',
                    align: 'left'
                },
                {
                    title: '保证金',
                    field: 'marginAmount',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        };
        $.table.init(options);
    });


    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#month',
            type: 'month',
            format:"MM"
        });
        laydate.render({
            elem: '#year',
            type: 'year'
        });
    });



</script>
</body>
</html>