<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <!--    <th:block th:include="include :: bootstrap-select-css" />-->
    <!-- Tabulator CSS -->
    <!--    <link href="https://cdnjs.cloudflare.com/ajax/libs/tabulator/6.3.0/css/tabulator.min.css" rel="stylesheet">-->
    <link th:href="@{/ajax/libs/tabulator/css/tabulator-6.3.min.css}" rel="stylesheet"/>

</head>
<style>
    /* 页面布局样式 */
    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    .container-fluid {
        height: 100vh;
        display: flex;
        flex-direction: column;
        padding: 10px;
        box-sizing: border-box;
    }

    .search-section {
        flex-shrink: 0;
        margin-bottom: 10px;
    }

    .table-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        position: relative;
    }

    /* Tabulator 自适应样式 */
    #tabulator-table {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 1px solid #ddd;
    }

    .tabulator .tabulator-header {
        background-color: #f5f5f5;
        border-bottom: 2px solid #ddd;
    }
    /* 添加这个新样式，确保所有分组行都有统一的背景色 */
    .tabulator-row.tabulator-group {
        background-color: #dadada !important;
    }

    .tabulator .tabulator-header .tabulator-col {
        background-color: #f5f5f5;
        border-right: 1px solid #ddd;
    }

    .tabulator .tabulator-row {
        border-bottom: 1px solid #ddd;
    }

    .tabulator .tabulator-row:nth-child(even) {
        background-color: #f9f9f9;
    }

    .tabulator .tabulator-row:hover {
        background-color: #f0f0f0;
    }

    /* 表格单元格样式 */
    .tabulator .tabulator-row .tabulator-cell {
        border-right: 1px solid #ddd;
        transition: all 0.3s ease;
    }

    /* 确保分组行内的单元格也有统一的背景色 */
    .tabulator-row.tabulator-group .tabulator-cell {
        background-color: #f8f9fa !important;
    }

    /* 地址内容样式 */
    .address-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.2;
        max-height: 20px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .address-content.expanded {
        white-space: normal;
        word-break: break-all;
        overflow: visible;
        line-height: 1.6;
        max-height: none;
    }

    /* 地址样式 */
    .address-province-city {
        color: #333333;
        font-weight: 500;
    }

    .address-area {
        color: #777777;
        font-weight: normal;
    }

    /* 分组样式 */
    .custom-group-header {
        background-color: #f8f9fa !important;
        font-weight: 600 !important;
        border-bottom: 1px solid #ddd !important;
    }

    .group-last-row .tabulator-cell {
        border-bottom: 2px solid #1c84c6;
    }

    /* 搜索表单样式 */
    .search-input {
        padding-left: 10px;
        margin-right: 15px;
    }

    .search-input .form-control {
        border-radius: 4px;
        border: 1px solid #ddd;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
        transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    }

    .search-input .form-control:focus {
        border-color: #66afe9;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
    }

    .search-input .input-label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #555;
    }

    .contract-none {
        color: #dc3545 !important;
        font-weight: 700 !important;
    }
    .contract-valid {
        color: #198754 !important;
        font-weight: 600 !important;
    }
    /* 合同状态颜色 */
    .contract-expired {
        color: #dc3545 !important;
        font-weight: 700 !important;
    }
</style>
<body class="gray-bg">
<div class="container-fluid">
    <div class="search-section">
        <form id="role-form" class="form-horizontal">
            <div class="row no-gutter">
                <!--          <div class="col-md-2 col-sm-2 search-input">
          &lt;!&ndash;                    <label class="input-label">客户简称</label>&ndash;&gt;
                              <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称" class="form-control" type="text" maxlength="20" autocomplete="off">
                          </div>-->

                <div class="col-md-2 col-sm-2 search-input">
                    <!--                    <label class="input-label">承运商</label>-->
                    <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control" type="text" maxlength="20" autocomplete="off">
                </div>

                <div class="col-md-3 col-sm-3">
                    <div class="form-group" style="text-align: left; ">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        <a class="btn btn-success btn-rounded btn-sm" onclick="analyzeExport()"><i class="fa fa-download"></i>&nbsp;导出数据</a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="table-section">
        <!-- Tabulator 表格容器 -->
        <div id="tabulator-table"></div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<!--<th:block th:include="include :: bootstrap-select-js" />-->
<!-- Tabulator JS -->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/tabulator/6.3.0/js/tabulator.min.js"></script>-->
<script th:src="@{/ajax/libs/tabulator/js/tabulator-6.3.min.js}"></script>

<script th:inline="javascript">
    var billing_type = [[${@dict.getType('billing_type')}]]; // 开票类型
    var billingMethod = [[${billingMethod}]];
    var table; // Tabulator实例

    $(function () {
        initTable();

        // 使用防抖处理窗口大小变化
        $(window).resize(debounce(function() {
            if (table) {
                table.redraw(true);
                styleGroups();
            }
        }, 150)); // 150ms防抖延迟
    });

    // 添加防抖函数
    function debounce(func, wait) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    function initTable() {
        // 销毁现有表格
        if (table) {
            table.destroy();
        }

        // 创建Tabulator表格
        table = new Tabulator("#tabulator-table", {
            ajaxURL: ctx + "autoDispatchConfig/auto_dispatch/analyze",
            ajaxConfig: "POST",
            ajaxContentType: "json",
            ajaxParams: getSearchParams(),
            height: "100%",
            layout: "fitColumns",
            resizableColumnFit:true,

            // 启用虚拟DOM（默认为true，确保不要设为false）
            virtualDom: true,
            // 设置虚拟DOM缓冲区大小，可以根据需要调整
            virtualDomBuffer: 300,

            responsiveLayout: "collapse",
            placeholder: "暂无数据",
            movableColumns: true,
            resizableColumns: true,
            tooltips: true,
            selectable: false,
            groupBy: "carrName",
            groupHeader: function(value, count, data, group) {
                var contractStatusClass = 'contract-none';
                var contractText = '<span class="contract-none">暂无合同</span>';

                if (data[0].invalidDate) {
                    var isExpired = isContractExpired(data[0].invalidDate);
                    contractStatusClass = isExpired ? 'contract-expired' : 'contract-valid';
                    var statusText = isExpired ? '已过期' : '有效期至';
                    contractText = `<span class="${contractStatusClass}">合同${statusText}：${data[0].invalidDate}</span>`;
                }

                return `
                    <i class="fa fa-truck" style="margin-right: 8px; color: #6c757d;"></i>
                    ${value}
                    <span style="color: #6c757d; font-weight: normal; margin-left: 8px;">(${count}条线路)</span>
                    <span style="float: right;">${contractText}</span>
                `;
            },
            groupToggleElement: ".group-header", // 整个头部都可以点击
            groupStartOpen: false, // 默认分组都缩起来
            renderVertical:"basic",

            // 数据处理配置
            ajaxResponse: function(url, params, response) {
                if (response && typeof response === 'object') {
                    if (Array.isArray(response)) {
                        return response;
                    } else if (response.data && Array.isArray(response.data)) {
                        return response.data;
                    } else if (response.rows && Array.isArray(response.rows)) {
                        return response.rows;
                    }
                }
                return [];
            },

            // 列定义
            columns: [
                {
                    title: "承运商名称",
                    field: "carrName",
                    width: 150,
                    frozen: true,
                    headerSort: false,
                    visible: false // 隐藏原始列，因为已经在分组中显示
                },
                {
                    title: "提货地址",
                    field: "deliProName",
                    width: 200,
                    frozen: true,
                    headerSort: false,
                    formatter: function(cell) {
                        var row = cell.getRow().getData();
                        var provinceCity = '<span class="address-province-city">' +
                            row.deliProName +
                            (row.deliCityName ? row.deliCityName : '') +
                            '</span>';
                        var area = row.deliAreaName ?
                            '<span class="address-area">[' + row.deliAreaName + ']</span>' : '';
                        return provinceCity + area;
                    }
                },
                {
                    title: "到货地址 <i class='fa fa-expand address-toggle-all' style='cursor:pointer;float:right;margin-right:10px;'></i>",
                    field: "arriProName",
                    width: 200,
                    headerSort: false,
                    formatter: function(cell) {
                        var row = cell.getRow().getData();
                        var fullAddress = '';

                        if (row.arriType == 0) {
                            var provinceCity = '<span class="address-province-city">' +
                                row.arriProName +
                                (row.arriCityName ? row.arriCityName : '') +
                                '</span>';
                            var area = row.arriAreaName ?
                                '<span class="address-area">[' + row.arriAreaName + ']</span>' : '';
                            fullAddress = provinceCity + area;
                        } else if (row.arriType == 1) {
                            fullAddress = row.arriAddrName || '';
                        }

                        return `<div class="address-content">${fullAddress}</div>`;
                    },
                    cellClick: function(e, cell) {
                        // 点击单元格内容时切换展开/收缩状态
                        var element = $(cell.getElement()).find('.address-content');
                        element.toggleClass('expanded');

                        // 使用requestAnimationFrame优化重绘
                        requestAnimationFrame(function() {
                            table.redraw(false);
                        });
                    }
                },
                {
                    title: '客户',
                    field : 'custAbbr',
                    width: 200,
                    frozen: true,
                    headerSort: false,
                },

                {
                    title: "计价方式",
                    field: "billingMethod",
                    width: 120,
                    headerSort: false,
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var htmlText = "";
                        billingMethod.forEach(function(res) {
                            if (res.value == value) {
                                htmlText = res.context;
                            }
                        });
                        return htmlText;
                    }
                },
                {
                    title: "车型",
                    field: "carLenName",
                    width: 120,
                    headerSort: false,
                    formatter: function(cell) {
                        var row = cell.getRow().getData();
                        var carLen = row.carLenName || '';
                        var carType = row.carTypeName || '';
                        return carLen ? (carLen + '米' + (carType ? carType : '')) : '';
                    }
                },
                {
                    title: "票点",
                    field: "billingType",
                    width: 100,
                    headerSort: false,
                    formatter: function(cell) {
                        var value = cell.getValue();
                        return $.table.selectDictLabel(billing_type, value);
                    }
                },
                {
                    title: "油卡",
                    field: "oilRatio",
                    width: 150,
                    headerSort: false,
                    formatter: function(cell) {
                        var row = cell.getRow().getData();
                        var value = cell.getValue();
                        var htmlText = "";

                        if (value) {
                            if (row.oilCostType == '1') {
                                htmlText = '预付油卡：'
                            } else if (row.oilCostType == '3') {
                                htmlText = '到付油卡：'
                            } else if (row.oilCostType == '5') {
                                htmlText = '回付油卡：'
                            }

                            if (row.oilType == 0) {
                                htmlText = htmlText + (value * 100) + '%';
                            } else if (row.oilType == 1) {
                                htmlText = htmlText + value + '元';
                            }
                        }

                        return htmlText;
                    }
                },
                {
                    title: "应付结算标准",
                    field: "deductionType",
                    width: undefined,  // 移除固定宽度
                    headerSort: false,
                    formatter: function(cell) {
                        var row = cell.getRow().getData();
                        var value = cell.getValue();

                        // 使用缓存变量减少重复计算
                        var htmlText = "";

                        // 简化条件判断，减少嵌套
                        if (value == 4 && row.sectionAmount) {
                            // 预处理数据，减少循环中的操作
                            var sections = row.sectionAmount.split(',');
                            var tableHtml = '<table class="table table-sm"><tbody>';

                            for (var i = 0; i < sections.length; i++) {
                                var parts = sections[i].trim().split(' ');
                                if (parts.length >= 2) {
                                    var rangeStr = parts[0];
                                    var priceStr = parts[1];
                                    var fixedStr = parts.length > 2 ? parts[2] : "";

                                    var xIndex = rangeStr.indexOf('x');
                                    if (xIndex > 0) {
                                        var min = rangeStr.substring(0, xIndex).trim();
                                        var max = rangeStr.substring(xIndex + 1).trim();

                                        var leftSymbol = min.endsWith('<=') ? '≤' : '<';
                                        min = min.replace(/<=|<$/, '');

                                        var rightSymbol = max.startsWith('<=') ? '≤' : '<';
                                        max = max.replace(/^<=|</, '');

                                        tableHtml += '<tr>' +
                                            '<td>' + min + '</td>' +
                                            '<td>' + leftSymbol + ' x ' + rightSymbol + '</td>' +
                                            '<td>' + max + '</td>' +
                                            '<td>' + priceStr + '</td>' +
                                            '<td>' + fixedStr + '</td>' +
                                            '</tr>';
                                    }
                                }
                            }

                            tableHtml += '</tbody></table>';
                            return tableHtml;
                        }

                        // 使用对象映射替代多个if-else
                        var typeMap = {
                            0: function() {
                                var amount = row.deductionAmount == null ? '' :
                                    row.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                return '应收扣减：' + amount;
                            },
                            1: function() {
                                var feeType = '';
                                if (row.deductionFeeType == 0) feeType = '(仅运费)';
                                else if (row.deductionFeeType == 1) feeType = '(运费+在途)';
                                return '应收扣减：' + (row.deductionAmount * 100) + '%' + feeType;
                            },
                            2: function() {
                                var amount = row.deductionAmount == null ? '' :
                                    row.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                return '固定应付总价：' + amount;
                            },
                            3: function() {
                                var amount = row.deductionAmount == null ? '' :
                                    row.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                return '固定应付单价：' + amount;
                            }
                        };

                        return (typeMap[value] && typeMap[value]()) || '';
                    }
                }
            ],

            // 数据加载完成后的回调
            dataLoaded: function(data) {
                console.log("数据加载完成:", data ? data.length : 0, " 行");
                // 处理分组样式
                setTimeout(styleGroups, 100);
            },

            // 表格渲染完成后的回调
            renderComplete: function() {
                console.log("表格渲染完成");
                // 处理分组样式
                setTimeout(styleGroups, 100);
            },

            // 错误处理
            ajaxError: function(xhr, textStatus, errorThrown) {
                console.error("数据加载失败:", textStatus, errorThrown);
                if (xhr.status === 0) {
                    console.error("网络连接错误或请求被取消");
                } else {
                    console.error("HTTP错误:", xhr.status, xhr.statusText);
                }
            }
        });
    }

    // 检查合同是否过期的函数
    function isContractExpired(invalidDate) {
        if (!invalidDate) return true; // 无合同视为过期
        var today = new Date();
        var expireDate = new Date(invalidDate);
        return expireDate < today;
    }

    // 修复分组头部显示 - 兼容Tabulator 6.3版本
    function styleGroups() {
        if (!table) return;

        try {
            // 使用requestAnimationFrame优化DOM操作
            requestAnimationFrame(function() {
                // 直接通过DOM查找分组行，而不是使用已废弃的getType方法
                var groupElements = document.querySelectorAll('#tabulator-table .tabulator-row.tabulator-group');

                // 批量处理DOM操作
                groupElements.forEach(function(element) {
                    // 使用classList而不是style.cssText
                    element.classList.add('custom-group-header');
                });

                // 处理最后一行样式 - 通过DOM查找而不是依赖getType
                var allGroups = table.getGroups();
                allGroups.forEach(function(group) {
                    try {
                        var groupRows = group.getRows();
                        if (groupRows && groupRows.length > 0) {
                            var lastRow = groupRows[groupRows.length - 1];
                            if (lastRow && lastRow.getElement) {
                                var lastElement = lastRow.getElement();
                                if (lastElement) {
                                    lastElement.classList.add('group-last-row');
                                }
                            }
                        }
                    } catch (groupError) {
                        console.warn("处理单个分组时出错:", groupError);
                    }
                });

                // 初始化地址显示状态
                $('.address-content').removeClass('expanded');
                $('.address-toggle').removeClass('fa-compress').addClass('fa-expand');
            });
        } catch (error) {
            console.error("处理分组样式时出错:", error);
        }
    }

    // 修改搜索功能
    function searchPre() {
        if (!table) {
            initTable();
            return;
        }

        var searchParams = getSearchParams();
        table.setData(ctx + "autoDispatchConfig/auto_dispatch/analyze", searchParams)
            .then(function() {
                console.log("数据加载成功");
                setTimeout(styleGroups, 100);
            })
            .catch(function(error) {
                console.error("数据加载失败:", error);
                initTable();
            });
    }

    // 重置功能
    function resetPre() {
        document.getElementById('role-form').reset();
        setTimeout(function() {
            searchPre();
        }, 100);
    }

    function analyzeExport() {
        $.modal.confirm("确定导出吗？", function () {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON('role-form');
            $.post(ctx + "autoDispatchConfig/auto_dispatch/analyze/export", search, function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    // 获取搜索参数
    function getSearchParams() {
        var formData = new FormData(document.getElementById('role-form'));
        var params = {};
        for (var pair of formData.entries()) {
            if (pair[1]) {
                params[pair[0]] = pair[1];
            }
        }
        return params;
    }

    // 导出数据
    function exportData() {
        if (!table) {
            alert("表格未初始化");
            return;
        }

        if (confirm("确定导出吗？")) {
            table.download("xlsx", "自动调度价格.xlsx", {
                sheetName: "自动调度价格"
            });
        }
    }

    // 目标导入功能
    function targetImport() {
        console.log("目标导入功能");
    }

    // 辅助函数：选择字典标签
    function selectDictLabel(dictArray, value) {
        if (!dictArray || !value) return '';

        for (var i = 0; i < dictArray.length; i++) {
            if (dictArray[i].value == value) {
                return dictArray[i].label || dictArray[i].text || dictArray[i].context || '';
            }
        }
        return value;
    }

    // 添加全局地址展开/收缩功能
    $(document).on('click', '.address-toggle-all', function() {
        var isExpanded = $(this).hasClass('fa-compress');
        var allAddresses = $('.address-content');

        if (isExpanded) {
            // 收缩所有地址
            allAddresses.removeClass('expanded');
            $(this).removeClass('fa-compress').addClass('fa-expand');

            // 重新调整行高
            setTimeout(function() {
                table.redraw(false);
            }, 50);
        } else {
            // 展开所有地址
            allAddresses.addClass('expanded');
            $(this).removeClass('fa-expand').addClass('fa-compress');

            // 重新调整行高
            setTimeout(function() {
                table.redraw(false);
            }, 50);
        }
    });
</script>

</body>
</html>