<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('发货单确认')" />
    <th:block th:include="include :: bootstrap-select-css"/>

</head>
<style>
    /*.order-wrap {*/
    /*    width: 500px;*/
    /*    margin: 0 auto;*/
    /*    padding: 20px;*/
    /*    background: #f5f5f5;*/
    /*    border-radius: 5px;*/
    /*}*/

    .order-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .order-item {
        background: #fff;
        padding: 5px 20px;
        margin-bottom: 15px;
        border-radius: 5px;
        box-shadow: 0 2px 6px rgba(0,0,0,.1);
    }

    .flex {
        display: flex;
        justify-content: space-between;
    }

    .order-header {
        align-items: center;
        margin-bottom: 2px;
    }

    .order-id {
        font-weight: 500;
        font-size: 20px;
    }
    .addr {
        font-weight: 500;
        font-size: 20px;
    }



    .route-info {
        line-height: 1.5;
        font-size: 14px;
    }

    .goods {
        margin-right: 5px;
        display: inline-block;
        background-color: #f5f5f5e8;
        padding-inline: 20px;
        padding-block: 1px;
        border: 1px solid #ccc;
    }
    .pricing {
        font-weight: normal;
        display: inline-block;
    }


    .order-options {
        font-size: 14px;
        display: flex;
        flex-direction: column;
    }

    .checkbox-label {
        padding: 2px;
        /*margin-right: 30px;*/
    }

    .order-billing {
        margin-bottom: 5px;
    }

    .amount {
        margin-left: 15px;
        font-size: 16px;
        font-weight: 600;
        /*color: #00a650;*/
    }

    .amount[onclick] {
        color: #007bff;
        text-decoration: underline;
        transition: color 0.3s ease;
    }

    .amount[onclick]:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .unit-price {
        margin-left: 10px;
        font-size: 15px;
        font-weight: 600;
        /*color: #00a650;*/
    }

    /*.order-payment {*/
    /*    border-top: 1px solid #ddd;*/
    /*    padding-top: 10px;*/
    /*    font-weight: bold;*/
    /*    font-size: 16px;*/
    /*}*/
    #autoDisDiv:empty {
        display: none;
    }

    /* 必填字段样式 */
    .required-field {
        border-left: 3px solid #f56c6c !important;
        position: relative;
    }

    .has-value {
        border-left: 3px solid #178872 !important;
    }

    .required-field::after {
        content: "必填";
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #f56c6c;
        font-size: 12px;
        pointer-events: none;
        background: white;
        padding: 0 2px;
    }

    /* 当输入框有值时隐藏必填提示 */
    .required-field.has-value::after {
        display: none;
    }

</style>
<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6 col-xs-6">

                </div>
            </div>

            <div class="row">
                <ul class="order-list">
                    <li class="order-item" th:each="affirm,affirmStat : ${affirmVOList}">
                        <input th:name="|affirmList[${affirmStat.index}].segmentId|" th:value="${affirm.segmentId}" type="hidden">
                        <div class="order-header flex">
                            <div class="route-info">
                                <div class="order-id">[[${affirm.vbillno}]]</div>
                                <div>
                                    <span class="addr">[[${affirm.deliAddr}]]</span>
                                    <i class="fa fa-long-arrow-right" aria-hidden="true"></i>
                                    <span class="addr">[[${affirm.arriAddr}]]</span>
                                </div>
                            </div>
                            <div>
                                <span class="goods">[[${affirm.goodsName}]]</span>

                                <span class="pricing">
                                    <select class="form-control valid custom-select" aria-invalid="false"
                                            th:data-segmentId="${affirm.segmentId}"
                                            th:data-index="${affirmStat.index}"
                                            th:name="|affirmList[${affirmStat.index}].billingMethod|"
                                            onchange="changeBillingMethod(this.value,this.getAttribute('data-segmentId'),this.getAttribute('data-index'))">
                                        <option th:each="billingMethod : ${billingMethods}"
                                                th:selected="${affirm.billingMethod} == ${billingMethod.value}"
                                                th:text="${billingMethod.context}"
                                                th:value="${billingMethod.value}">
                                        </option>
                                    </select>

                                </span>
                            </div>
                        </div>
                        <div class="order-options">
                            <div th:id="|autoDisDiv_${affirmStat.index}|" style="" >
                                <div th:if="${affirm.billingMethod} != 7 and ${not #lists.isEmpty(affirm.autoConfigList)}">
                                    <input th:name="|affirmList[${affirmStat.index}].autoDispatchConfigId|"
                                           th:id="|autoDispatchConfigId_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].autoDispatchConfigId}" type="hidden">

                                    <input th:name="|affirmList[${affirmStat.index}].payTotalFee|"
                                           th:id="|payTotalFee_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].payTotalFee}" type="hidden">
                                    <input th:name="|affirmList[${affirmStat.index}].payUnitPrice|"
                                           th:id="|payUnitPrice_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].payUnitPrice}" type="hidden">

                                    <input th:name="|affirmList[${affirmStat.index}].oilAmount|"
                                           th:id="|oilAmount_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].oilAmount}" type="hidden">

                                    <input th:name="|affirmList[${affirmStat.index}].cashAmount|"
                                           th:id="|cashAmount_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].cashAmount}" type="hidden">

                                    <input th:name="|affirmList[${affirmStat.index}].otherFee|"
                                           th:id="|otherFee_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].otherFee}" type="hidden">
                                    <input th:name="|affirmList[${affirmStat.index}].otherFeeType|"
                                           th:id="|otherFeeType_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].otherFeeType}" type="hidden">


                                    <input th:name="|affirmList[${affirmStat.index}].carrierId|"
                                           th:id="|carrierId_${affirmStat.index}|"
                                           th:value="${affirm.autoConfigList[0].carrierId}" type="hidden">

                                    <span>
<!--                                        <label class="checkbox-label">-->
<!--                                            <input th:onchange="|changeIsAutoDispatch(this,${affirmStat.index})|"-->
<!--                                                   type="checkbox" style="margin-right: 5px;">-->
<!--                                            <input th:name="|affirmList[${affirmStat.index}].isAutoDispatch|"-->
<!--                                                   th:id="|isAutoDispatch_${affirmStat.index}|"-->
<!--                                                   type="hidden" value="0" >-->
<!--                                            <span style="user-select:none;">调度规则匹配</span>-->
<!--                                        </label>-->
                                    <input th:name="|affirmList[${affirmStat.index}].isAutoDispatch|"
                                           th:id="|isAutoDispatch_${affirmStat.index}|"
                                           type="hidden" value="1" >

                                    </span>
                                    <span style="margin: 3px;display: inline-block;">
                                        <select class="form-control valid custom-select" aria-invalid="false"
                                                th:id="|carr_${affirmStat.index}|"
                                                th:onchange="|changeAutoConfig(this,${affirmStat.index});calculateProfit(${affirmStat.index})|">
                                            <option th:each="dict : ${affirm.autoConfigList}"
                                                    th:data-paytotalfee="${dict.payTotalFee}"
                                                    th:data-payunitprice="${dict.payUnitPrice}"
                                                    th:data-carrierid="${dict.carrierId}"
                                                    th:data-hasonway="${dict.hasOnWay}"
                                                    th:data-otherfee="${dict.otherFee}"
                                                    th:data-otherfeetype="${dict.otherFeeType}"
                                                    th:data-otherfeetypetext="${dict.otherFeeTypeText}"
                                                    th:data-oilamount="${dict.oilAmount}"
                                                    th:data-cashamount="${dict.cashAmount}"
                                                    th:data-billingtype="${dict.billingType}"
                                                    th:data-requiredflag="${dict.requiredFlag}"
                                                    th:text="|承运商：${dict.carrName}|"
                                                    th:value="${dict.autoDispatchConfigId}">
                                            </option>
                                        </select>
                                    </span>
                                    <span style="margin: 3px;display: inline-block;">
                                        <input th:id="|carno_${affirmStat.index}|"
                                               th:onclick="|selectCar(${affirmStat.index})|"
                                               placeholder="可选择车牌号" class="form-control valid"
                                               type="text"  autocomplete="off" readonly>
                                        <input th:name="|affirmList[${affirmStat.index}].carnoId|"
                                               th:id="|carnoId_${affirmStat.index}|"
                                               type="hidden">
                                    </span>
                                    <span style="margin: 3px;display: inline-block;">
                                        <input th:id="|driverName_${affirmStat.index}|"
                                               th:onclick="|selectDriver(${affirmStat.index})|"
                                               placeholder="可选择司机" class="form-control valid" type="text" readonly>
                                        <input th:name="|affirmList[${affirmStat.index}].driverId|"
                                               th:id="|driverId_${affirmStat.index}|"
                                               type="hidden">
                                    </span>

                                    <span style="margin: 3px;display: inline-block;">
                                        <select th:name="|affirmList[${affirmStat.index}].carrBankId|"
                                                th:id="|carrBankId_${affirmStat.index}|"
                                                class="form-control valid custom-select selectpicker"
                                                data-live-search="true" data-none-selected-text="请选择收款账号" aria-invalid="false">
                                            <option value=""></option>
                                        </select>
                                    </span>

                                    <span style="margin: 3px;display: inline-block;" th:if="${affirm.autoDispatchDeliFee == 1}">
<!--                                        <div class="input-group">-->
                                            <input th:id="|deliveryFee_${affirmStat.index}|"
                                                   th:name="|affirmList[${affirmStat.index}].deliveryFee|"
                                                   th:oninput="|$.numberUtil.onlyNumberTwoDecimal(this);calculateProfit(${affirmStat.index})|"
                                                   placeholder="送货费" class="form-control valid" type="text">
                                        <!--                                            <span class="input-group-addon">元</span>-->
                                        <!--                                        </div>-->
                                    </span>
                                    <span style="margin: 3px;display: inline-block;" th:if="${affirm.autoDispatchPickUpFee == 1}">
<!--                                        <div class="input-group">-->
                                            <input th:id="|pickUpFee_${affirmStat.index}|"
                                                   th:name="|affirmList[${affirmStat.index}].pickUpFee|"
                                                   th:oninput="|$.numberUtil.onlyNumberTwoDecimal(this);calculateProfit(${affirmStat.index})|"
                                                   placeholder="提货费" class="form-control valid" type="text">
                                        <!--                                            <span class="input-group-addon">元</span>-->
                                        <!--                                        </div>-->
                                    </span>

                                </div>
                            </div>
<!--                            <div>-->
<!--                                <label class="checkbox-label" th:if="${!#strings.isEmpty(affirm.autoSubsectionAddrId)}">-->
<!--                                    <input th:onchange="|changeIsSubsection(this,${affirmStat.index})|"-->
<!--                                           type="checkbox" style="margin-right: 5px;">-->
<!--                                    <input th:name="|affirmList[${affirmStat.index}].isSubsection|"-->
<!--                                           th:id="|isSubsection_${affirmStat.index}|"-->
<!--                                           type="hidden" value="0" >-->
<!--                                    <span style="user-select:none;">拆段提货（中转站点：[[${affirm.autoSubsectionAddrName}]]）</span>-->
<!--                                </label>-->
<!--                            </div>-->
                        </div>

                        <div th:id="|payDiv_${affirmStat.index}|" class="order-payment" >
                            <div style="display: flex;align-items: center;" th:if="${not #lists.isEmpty(affirm.autoConfigList)}">
                                <span class="label label-warning" style="display: inline-block;">应付</span>
                                <span th:if="${@permission.hasAnyPermi('tms:segment:autoDisEditPrice') == ''}"
                                      th:id="|payTotalFeeText_${affirmStat.index}|" class="amount"
                                      style="display: inline-block; cursor: pointer;"
                                      th:onclick="|editPayTotalFee(${affirmStat.index}, ${affirm.autoConfigList[0].payTotalFee})|"
                                      title="点击修改金额">[[${#numbers.formatCurrency(affirm.autoConfigList[0].payTotalFee)}]]</span>

                                <span th:if="${@permission.lacksPermi('tms:segment:autoDisEditPrice') == ''}"
                                      th:id="|payTotalFeeText_${affirmStat.index}|" class="amount" style="display: inline-block;">
                                    [[${#numbers.formatCurrency(affirm.autoConfigList[0].payTotalFee)}]]
                                </span>

                                <span th:id="|payUnitPriceText_${affirmStat.index}|" class="unit-price" style="display: inline-block;">单价:[[${#numbers.formatCurrency(affirm.autoConfigList[0].payUnitPrice)}]]</span>

                                <span th:if="${affirm.autoConfigList[0].hasOnWay == '1'}"
                                      th:id="|otherFeeText_${affirmStat.index}|" class="unit-price"
                                      style="display: inline-block;">[[${affirm.autoConfigList[0].otherFeeTypeText}]]:[[${#numbers.formatCurrency(affirm.autoConfigList[0].otherFee)}]]</span>

                                <span>
                                    <span th:each="yfMiscFee : ${affirm.yfMiscFeeList}" th:if="${affirm.yfMiscFeeList != null and !#lists.isEmpty(affirm.yfMiscFeeList)}" class="unit-price" style="display: inline-block; margin-left: 10px;">
                                        <span style="cursor: pointer;"
                                              th:title="'到货地址：' + ${yfMiscFee.arriAddrName} + ' '
                                                + (${yfMiscFee.billingMethod} == '1' ? ${yfMiscFee.weight} + '(重量)' :
                                                   (${yfMiscFee.billingMethod} == '2' ? ${yfMiscFee.volume} + '(体积)' :
                                                   (${yfMiscFee.billingMethod} == '5' ? ${yfMiscFee.num} + '(件数)' : '')))
                                                + ' * ' + ${yfMiscFee.miscYfAmount} + '(单价)'">
                                            [[${yfMiscFee.costTypeName}]]
                                        </span>
                                        :[[${#numbers.formatCurrency(yfMiscFee.miscYfAmountTotal)}]]
                                    </span>
                                </span>
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <div th:id="|negativeGrossProfitDiv_${affirmStat.index}|" style="display: none;">
                                <span class="label label-danger">负毛利区间</span>
                                <span th:id="|negativeGrossProfit_${affirmStat.index}|" style="color: #ed5565;font-size: 16px;font-weight: 600;"></span>
                            </div>
                        </div>

                    </li>
                </ul>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:src="@{/js/decimal.js}"></script>
<th:block th:include="include :: bootstrap-select-js"/>

<script th:inline="javascript">
    var affirmVOList = [[${affirmVOList}]];
    var oilTax = [[${@sysConfigServiceImpl.selectConfigByKey("oil_tax_rate")}]];
    var cashTax = [[${@sysConfigServiceImpl.selectConfigByKey("cash_tax_rate")}]];
    var canEditPayTotalFee = [[${@permission.hasAnyPermissions('tms:segment:autoDisEditPrice')}]];

    $(function () {
        for (let i = 0; i < affirmVOList.length; i++) {
            updateFieldRequiredStatus(i);
            // 初始化selectpicker
            $(`#carrBankId_${i}`).selectpicker();
            loadCarrBankData(i);
            calculateProfit(i)
        }

    });

    function changeIsAutoDispatch(checkbox, ind) {
        // 获取checkbox的选中状态
        var checked = checkbox.checked;
        // 根据选中状态设置值
        if (checked) {
            $("#isAutoDispatch_" + ind).val(1)
        } else {
            $("#isAutoDispatch_" + ind).val(0)
        }
    }

    // function changeIsSubsection(checkbox, ind) {
    //     // 获取checkbox的选中状态
    //     var checked = checkbox.checked;
    //     // 根据选中状态设置值
    //     if(checked) {
    //         $("#isSubsection_" + ind).val(1)
    //     } else {
    //         $("#isSubsection_" + ind).val(0)
    //     }
    // }

    function changeAutoConfig(selectElement,ind) {

        // 获取选中的 option 元素
        var selectedOption = $(selectElement).find('option:selected');

        // 获取data属性值
        var payTotalFee = selectedOption.data('paytotalfee');
        var payUnitPrice = selectedOption.data('payunitprice');
        var oilAmount = selectedOption.data('oilamount');
        var cashAmount = selectedOption.data('cashamount');
        var carrierId = selectedOption.data('carrierid');

        var hasOnWay = selectedOption.data('hasonway');
        var otherFee = selectedOption.data('otherfee');
        var otherFeeType = selectedOption.data('otherfeetype');
        var otherFeeTypeText = selectedOption.data('otherfeetypetext');
        var requiredFlag = selectedOption.data('requiredflag');

        var autoDispatchConfigId = selectedOption.val();

        // 将金额格式化为￥33,411.00
        let payTotalFeeText = formatCurrency(payTotalFee);
        let payUnitPriceText = formatCurrency(payUnitPrice);


        if (canEditPayTotalFee) {
            $(`#payTotalFeeText_${ind}`).html(payTotalFeeText)
                .attr('onclick', `editPayTotalFee(${ind}, ${payTotalFee})`)
                .attr('title', '点击修改金额')
                .css('cursor', 'pointer');
        }else{
            $(`#payTotalFeeText_${ind}`).html(payTotalFeeText)
        }
        $(`#payUnitPriceText_${ind}`).text("单价:"+payUnitPriceText)


        $(`#autoDispatchConfigId_${ind}`).val(autoDispatchConfigId)
        $(`#payTotalFee_${ind}`).val(payTotalFee)
        $(`#oilAmount_${ind}`).val(oilAmount)
        $(`#cashAmount_${ind}`).val(cashAmount)
        $(`#payUnitPrice_${ind}`).val(payUnitPrice)
        $(`#carrierId_${ind}`).val(carrierId)


        if (hasOnWay == 1) {
            $(`#otherFeeText_${ind}`).text(otherFeeTypeText + ":" + formatCurrency(otherFee))
            $(`#otherFee_${ind}`).val(otherFee)
            $(`#otherFeeType_${ind}`).val(otherFeeType)

        } else {
            $(`#otherFeeText_${ind}`).text("")
            $(`#otherFee_${ind}`).val("")
            $(`#otherFeeType_${ind}`).val("")

        }

        // 根据requiredFlag设置车牌号和司机的必填状态
        updateFieldRequiredStatus(ind);

        loadCarrBankData(ind)
    }

    /**
     * 根据requiredFlag设置车牌号和司机的必填状态
     * @param ind 索引
     */
    function updateFieldRequiredStatus(ind) {
        var carrSelect = $(`#carr_${ind}`);
        if (carrSelect.length === 0) {
            return;
        }
        var selectedOption = carrSelect.find('option:selected');
        //必填标识：1-车辆司机必填，2-车辆必填，3-司机必填，4-都不必填
        var requiredFlag = selectedOption.data('requiredflag') || 4;

        var carnoInput = $(`#carno_${ind}`);
        var driverNameInput = $(`#driverName_${ind}`);
        let isAutoDispatch = $("#isAutoDispatch_" + ind).val();

        // 移除之前的必填属性和样式
        carnoInput.removeAttr("required").removeClass("required-field").removeClass("has-value");
        driverNameInput.removeAttr("required").removeClass("required-field").removeClass("has-value");

        // 移除之前的事件监听
        carnoInput.off('input.required');
        driverNameInput.off('input.required');

        if (isAutoDispatch == 0) {
            return
        }

        if (requiredFlag == 1) {
            // 车辆司机必填
            carnoInput.addClass("required-field");
            driverNameInput.addClass("required-field");
            setupRequiredFieldListener(carnoInput);
            setupRequiredFieldListener(driverNameInput);
        } else if (requiredFlag == 2) {
            // 车辆必填
            carnoInput.addClass("required-field");
            setupRequiredFieldListener(carnoInput);
        } else if (requiredFlag == 3) {
            // 司机必填
            driverNameInput.addClass("required-field");
            setupRequiredFieldListener(driverNameInput);
        }
        // requiredFlag === 4 或其他值时，都不必填（已在上面移除了必填属性）

        // 初始检查输入框是否有值
        checkFieldValue(carnoInput);
        checkFieldValue(driverNameInput);
    }

    /**
     * 为必填字段设置监听器
     */
    function setupRequiredFieldListener(input) {
        input.on('input.required', function() {
            checkFieldValue($(this));
        });
    }

    /**
     * 检查字段值并更新样式
     */
    function checkFieldValue(input) {
        if (input.hasClass('required-field')) {
            if (input.val() && input.val().trim() !== '') {
                input.addClass('has-value');
            } else {
                input.removeClass('has-value');
            }
        }
    }

    /**
     * 查询承运商下的收款账户
     */
    function loadCarrBankData(index){
        let carrierId = $(`#carrierId_${index}`).val();

        let options = '<option value=""></option>';
        $(`#carrBankId_${index}`).html(options);
        if('' == carrierId)return ;
        $.ajax({
            url: ctx + "tms/segment/queryCarrierBankList/"+carrierId,
            type: 'post',
            dataType: 'json',
            data: {},
            success: function(res) {
                let dataList = res.data;
                if(null != dataList){
                    for(let idx in dataList){
                        let carrBankId = dataList[idx].carrBankId;
                        let bankAccount = dataList[idx].bankAccount;
                        let bankCard = dataList[idx].bankCard;
                        let phone = dataList[idx].phone == null ? '' : dataList[idx].phone;

                        $(`#carrBankId_${index}`).append('<option value="'+carrBankId+'">'+bankAccount+"-"+bankCard+"-"+phone+'</option>');
                    }
                }
                // 刷新selectpicker
                $(`#carrBankId_${index}`).selectpicker('refresh');
            }
        })
    }

    function formatCurrency(amount) {
        // 将字符串金额转为数字
        var numericAmount = parseFloat(amount);

        // 判断是否为数字
        if (!isNaN(numericAmount)) {
            // 格式化为金额形式
            return '￥' + numericAmount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
        }

        return amount; // 如果无法转换为数字，返回原始值
    }

    function changeBillingMethod(selectedValue, id, index) {
        $.ajax({
            url: ctx + "autoDispatchConfig/getPrice",
            method: 'get',
            dataType: "json",
            data: {billingMethod: selectedValue, id: id, type: 1},
            async: false,
            success: function (res) {
                if (res.code === 0) {
                    let data = res.data;
                    $(`#autoDisDiv_${index}`).empty();
                    $(`#payDiv_${index}`).empty();
                    $(`#negativeGrossProfit_${index}`).html("");
                    $(`#negativeGrossProfitDiv_${index}`).hide();


                    if (data.type === "1") {
                        let dataList = data.dataList;

                        let autoDispatchDeliFee = data.autoDispatchDeliFee;

                        let deliFeeHtml = ''
                        if (autoDispatchDeliFee == 1) {
                            deliFeeHtml = `
                                <span style="margin: 3px;display: inline-block;">
                                    <input id="deliveryFee_${index}"
                                           name="affirmList[${index}].deliveryFee"
                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateProfit(${index})"
                                           placeholder="送货费" class="form-control valid" type="text">
                                </span>
                            `
                        }

                        let autoDispatchPickUpFee = data.autoDispatchPickUpFee;

                        let pickUpFeeHtml = ''
                        if (autoDispatchPickUpFee == 1) {
                            pickUpFeeHtml = `
                                <span style="margin: 3px;display: inline-block;">
                                    <input id="pickUpFee_${index}"
                                           name="affirmList[${index}].pickUpFee"
                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateProfit(${index})"
                                           placeholder="送货费" class="form-control valid" type="text">
                                </span>
                            `
                        }

                        let html = ``;
                        if (dataList.length > 0) {
                            let bilHtml = `
                                    <select class="form-control valid custom-select" aria-invalid="false"
                                            id="carr_${index}"
                                            onchange="changeAutoConfig(this,${index});calculateProfit(${index})">
                            `

                            dataList.forEach(x => {
                                bilHtml += `<option data-paytotalfee="${x.payTotalFee}"
                                                    data-payunitprice="${x.payUnitPrice}"
                                                    data-carrierid="${x.carrierId}"
                                                    data-hasonway="${x.hasOnWay}"
                                                    data-otherfee="${x.otherFee == undefined || x.otherFee == null ? '' : x.otherFee}"
                                                    data-otherfeetype="${x.otherFeeType == undefined || x.otherFeeType == null ? '' : x.otherFeeType}"
                                                    data-otherfeetypetext="${x.otherFeeTypeText == undefined || x.otherFeeTypeText == null ? '' : x.otherFeeTypeText}"
                                                    data-oilamount="${x.oilAmount == undefined || x.oilAmount == null ? '': x.oilAmount}"
                                                    data-cashamount="${x.cashAmount == undefined || x.cashAmount == null ? '': x.cashAmount}"
                                                    data-billingtype="${x.billingType}"
                                                    data-requiredflag="${x.requiredFlag == undefined || x.requiredFlag == null ? '4': x.requiredFlag}"
                                                    value="${x.configId}">
                                                    承运商：${x.carrName}
                                             </option>
                                `

                            });

                            bilHtml += `</select>`

                            html = `
                            <div>
                                <input name="affirmList[${index}].autoDispatchConfigId"
                                       id="autoDispatchConfigId_${index}"
                                       value="${dataList[0].configId}" type="hidden"/>

                                <input name="affirmList[${index}].payTotalFee"
                                       id="payTotalFee_${index}"
                                       value="${dataList[0].payTotalFee}" type="hidden">
                                <input name="affirmList[${index}].payUnitPrice"
                                       id="payUnitPrice_${index}"
                                       value="${dataList[0].payUnitPrice}" type="hidden">
                                <input name="affirmList[${index}].oilAmount"
                                       id="oilAmount_${index}"
                                       value="${dataList[0].oilAmount}" type="hidden">
                                <input name="affirmList[${index}].cashAmount"
                                       id="cashAmount_${index}"
                                       value="${dataList[0].cashAmount}" type="hidden">
                                <input name="affirmList[${index}].otherFee"
                                       id="otherFee_${index}"
                                       value="${dataList[0].otherFee == undefined || dataList[0].otherFee == null ? '' : dataList[0].otherFee}" type="hidden">
                                <input name="affirmList[${index}].otherFeeType"
                                       id="otherFeeType_${index}"
                                       value="${dataList[0].otherFeeType == undefined || dataList[0].otherFeeType == null ? '' :dataList[0].otherFeeType}" type="hidden">

                                <input name="affirmList[${index}].carrierId"
                                       id="carrierId_${index}"
                                       value="${dataList[0].carrierId}" type="hidden">
                                <span>
<!--                                    <label class="checkbox-label">-->
<!--                                        <input onchange="changeIsAutoDispatch(this,${index})"-->
<!--                                               type="checkbox" style="margin-right: 5px;">-->
<!--                                        <input name="affirmList[${index}].isAutoDispatch"-->
<!--                                               id="isAutoDispatch_${index}"-->
<!--                                               type="hidden" value="0" >-->
<!--                                        <span style="user-select:none;">调度规则匹配</span>-->
<!--                                    </label>-->
                                    <input name="affirmList[${index}].isAutoDispatch"
                                           id="isAutoDispatch_${index}"
                                           type="hidden" value="1" >

                                </span>
                                <span style="margin: 3px;display: inline-block;">
                                    ${bilHtml}
                                </span>
                                <span style="margin: 3px;display: inline-block;">
                                    <input id="carno_${index}"
                                           onclick="selectCar(${index})"
                                           placeholder="可选择车牌号" class="form-control valid"
                                           type="text"  autocomplete="off" readonly>
                                    <input name="affirmList[${index}].carnoId"
                                           id="carnoId_${index}"
                                           type="hidden">
                                </span>
                                <span style="margin: 3px;display: inline-block;">
                                    <input id="driverName_${index}"
                                           onclick="selectDriver(${index})"
                                           placeholder="可选择司机" class="form-control valid" type="text" readonly>
                                    <input name="affirmList[${index}].driverId"
                                           id="driverId_${index}"
                                           type="hidden">
                                </span>
                                <span style="margin: 3px;display: inline-block;">
                                    <select name="affirmList[${index}].carrBankId"
                                            id="carrBankId_${index}"
                                            class="form-control valid custom-select selectpicker" data-none-selected-text="请选择收款账号" data-live-search="true" aria-invalid="false">
                                        <option value=""></option>
                                    </select>
                                </span>

                                ${deliFeeHtml}
                                ${pickUpFeeHtml}
                            </div>

                            `;

                            $(`#autoDisDiv_${index}`).prepend(html);

                            let payHtml= `
                                <div style="display: flex;align-items: center;">
                                    <span class="label label-warning" style="display: inline-block;">应付</span>
                                    ${
                                        canEditPayTotalFee
                                        ? `<span id="payTotalFeeText_${index}" class="amount"
                                                     style="display: inline-block; cursor: pointer;"
                                                     onclick="editPayTotalFee(${index}, ${dataList[0].payTotalFee})"
                                                     title="点击修改金额">￥${dataList[0].payTotalFee}</span>`
                                        : `<span id="payTotalFeeText_${index}" class="amount"
                                                         style="display: inline-block;">￥${dataList[0].payTotalFee}</span>`
                                    }
                                    <span id="payUnitPriceText_${index}" class="unit-price"
                                          style="display: inline-block;">单价:￥${dataList[0].payUnitPrice}</span>
                            `
                            if (dataList[0].hasOnWay == 1) {
                                payHtml += `
                                    <span id="otherFeeText_${index}" class="unit-price"
                                          style="display: inline-block;">${dataList[0].otherFeeTypeText}:
                                          ￥${dataList[0].otherFee}</span>
                                `
                            }

                            let yfMiscFeeHtml = getYfMiscFeeHtml(index);

                            payHtml += `
                                ${yfMiscFeeHtml}
                                </div>
                            `

                            $(`#payDiv_${index}`).prepend(payHtml);

                            // 设置第一个选项的必填状态
                            if (dataList.length > 0) {
                                updateFieldRequiredStatus(index);
                            }

                            // 初始化selectpicker
                            $(`#carrBankId_${index}`).selectpicker();
                            loadCarrBankData(index);
                            calculateProfit(index)
                        }
                    }
                }
            }
        })
    }

    /**
     *  选择选择车辆
     */
    function selectCar(ind){
        var carrierId = $(`#carrierId_${ind}`).val();

        parent.layer.open({
            type: 2,
            area: ['80%', '80%'],
            // fix: true,
            maxmin: true,
            shade: 0.3,
            title: "选择车辆",
            content: ctx + `basic/car/selectCarLic?carrierId=${carrierId}&type=3`,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var rows = layero.find('iframe')[0].contentWindow.getChecked();

                if (rows.length === 0) {
                    parent.$.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                if(rows[0]["isblacklist"] != '0'){
                    parent.$.modal.alertWarning("请选择非黑名单车辆");
                    return;
                }

                $(`#carnoId_${ind}`).val("");
                $(`#carno_${ind}`).val("");

                //车辆id
                $(`#carnoId_${ind}`).val(rows[0]["carId"]);
                //车牌
                $(`#carno_${ind}`).val(rows[0]["carno"]);

                // 更新必填字段样式
                checkFieldValue($(`#carno_${ind}`));

                parent.layer.close(index);

            },
            cancel: function (index) {
                return true;
            }
        });
    }

    /**
     * 选择司机
     */
    function selectDriver(ind) {
        //承运商id
        var carrierId = $(`#carrierId_${ind}`).val();

        parent.layer.open({
            type: 2,
            area: ['90%', '90%'],
            // fix: true,
            maxmin: true,
            shade: 0.3,
            title: "选择司机",
            content:ctx + `basic/driver/checkboxSelectDriver?type=3&carrierId=${carrierId}`,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var rows = layero.find('iframe')[0].contentWindow.getChecked();

                if (rows.length === 0) {
                    parent.$.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                if(rows[0]["isblacklist"] != '0'){
                    parent.$.modal.alertWarning("请选择非黑名单司机");
                    return;
                }
                //清空数据
                $(`#driverName_${ind}`).val("");
                $(`#driverId_${ind}`).val("");

                //回填数据
                $(`#driverName_${ind}`).val(rows[0]["driverName"]);
                $(`#driverId_${ind}`).val(rows[0]["driverId"]);

                // 更新必填字段样式
                checkFieldValue($(`#driverName_${ind}`));

                parent.layer.close(index);

            },
            cancel: function (index) {
                return true;
            }
        });

    }

    function getYfMiscFeeHtml(ind) {
        let affirmVO = affirmVOList[ind];
        let yfMiscFeeList = affirmVO.yfMiscFeeList;
        if (!yfMiscFeeList || yfMiscFeeList.length === 0) {
            return "<span></span>";
        }

        let html = '<span>';

        yfMiscFeeList.forEach(yfMiscFee => {
            // 构造 title 内容
            let title = "到货地址：" + (yfMiscFee.arriAddrName || "") + " ";
            if (yfMiscFee.billingMethod === '1') {
                title += (yfMiscFee.weight || '') + '(重量)';
            } else if (yfMiscFee.billingMethod === '2') {
                title += (yfMiscFee.volume || '') + '(体积)';
            } else if (yfMiscFee.billingMethod === '5') {
                title += (yfMiscFee.num || '') + '(件数)';
            }
            title += " * " + (yfMiscFee.miscYfAmount || '') + "(单价)";

            // 格式化金额（模拟 Thymeleaf #numbers.formatCurrency）
            let total = Number(yfMiscFee.miscYfAmountTotal || 0).toLocaleString('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            });

            html += `
            <span class="unit-price" style="display: inline-block; margin-left: 10px;">
                <span style="cursor: pointer;" title="${title}">
                    ${yfMiscFee.costTypeName || ''}
                </span>
                :${total}
            </span>
        `;
        });

        html += '</span>';
        return html;
    }


    // 安全转换为Decimal，处理空值
    function toDecimal(val) {
        if (val === null || val === undefined || val === '' || isNaN(val)) {
            return new Decimal(0);
        }
        return new Decimal(val);
    }
    // 累加多个值
    function decimalSum(...values) {
        return values.reduce((sum, val) => sum.plus(toDecimal(val)), new Decimal(0));
    }

    function calculateProfit(ind) {
        let affirmVO = affirmVOList[ind];
        //发货单票点
        let isNoinv = affirmVO.billingType != null && affirmVO.billingType == 6 ? 1 : 0;
        let profit = affirmVO.profit;
        // let totalFee = affirmVO.totalFee;
        /*
         * 自动调度配置金额 与 页面输入的在途金额
         */
        let carrSelectedOption = $(`#carr_${ind}`).find('option:selected');
        //自动调度 应付 票点
        var billingType = carrSelectedOption.data('billingtype');
        //自动调度 应付 油卡
        var oilAmount = $(`#oilAmount_${ind}`).val();
        //自动调度 应付 现金
        var cashAmount = $(`#cashAmount_${ind}`).val();
        //自动调度  其他费
        var otherfee = carrSelectedOption.data('otherfee');

        //页面输入 送货费
        let deliveryFee = $(`#deliveryFee_${ind}`).val();
        //页面输入 提货费
        let pickUpFee = $(`#pickUpFee_${ind}`).val();

        let baseResult = calculateExclusionTax(billingType, isNoinv, oilAmount, cashAmount, decimalSum(deliveryFee, pickUpFee, otherfee));

        /*
         * 自动配置的应付在途费
         */
        let yfMiscResult = new Decimal(0);
        let yfMiscFeeList = affirmVO.yfMiscFeeList;
        for (let i = 0; i < yfMiscFeeList.length; i++) {
            let miscFee = yfMiscFeeList[i];
            if (!miscFee) continue;

            let miscBillingType = miscFee.yfBillingType;
            let miscOtherSum = miscFee.miscYfAmountTotal;
            //
            let miscResult = calculateExclusionTax(miscBillingType, isNoinv, 0, 0, miscOtherSum);
            // 累加到总结果
            yfMiscResult = yfMiscResult.plus(toDecimal(miscResult));
        }

        let yfResult = decimalSum(baseResult, yfMiscResult);

        /*
         * 应收
         */
        let ysMiscResult = new Decimal(0);
        // let ysMiscFeeList = affirmVO.ysMiscFeeList;
        // for (let i = 0; i < ysMiscFeeList.length; i++) {
        //     let miscFee = ysMiscFeeList[i];
        //     if (!miscFee) continue;
        //
        //     let miscOtherSum = miscFee.miscYsAmountTotal;
        //     // 累加到总结果
        //     ysMiscResult = ysMiscResult.plus(toDecimal(miscOtherSum));
        // }

        let html = calculateNegativeGrossProfitHtml(yfResult, decimalSum(profit, ysMiscResult));

        if (html !== '') {
            $(`#negativeGrossProfit_${ind}`).html(html);
            $(`#negativeGrossProfitDiv_${ind}`).show();
        }else {
            $(`#negativeGrossProfit_${ind}`).html("");
            $(`#negativeGrossProfitDiv_${ind}`).hide();
        }
    }

    function calculateExclusionTax(billingType, isNoinv, oilSum, cashSum, otherSum) {
        const oil = toDecimal(oilSum);
        const cash = toDecimal(cashSum);
        const other = toDecimal(otherSum);

        // 这些变量需要在其他地方定义
        const oTax = toDecimal(oilTax || 0);
        const cTax = toDecimal(cashTax || 0);

        let msrp2;

        if (isNoinv === 1) {
            msrp2 = decimalSum(oil, cash, other);
        } else {
            msrp2 = decimalSum(oil, oil.mul(oTax));

            if (billingType == 6) {
                msrp2 = msrp2.plus(decimalSum(cash, cash.mul(cTax)));
                msrp2 = msrp2.plus(decimalSum(other, other.mul(cTax)));
            } else {
                msrp2 = msrp2.plus(decimalSum(cash, other));
            }
        }

        return msrp2.toFixed(2);
    }

    function calculateNegativeGrossProfitHtml(msrp2,profit) {
        const msrp2Decimal = toDecimal(msrp2);
        const profitDecimal = toDecimal(profit);

        let html = ''

        if (profitDecimal.gt(0) && msrp2Decimal.gt(profitDecimal)) {
            // 使用 Decimal 进行计算，避免精度丢失
            let negativeGrossProfit = msrp2Decimal.minus(profitDecimal);
            let negativeGrossProfitNum = parseFloat(negativeGrossProfit.toFixed(2));

            if (negativeGrossProfitNum >= 0 && negativeGrossProfitNum < 20) {
                html = '[0-20]';
            } else if (negativeGrossProfitNum >= 20 && negativeGrossProfitNum < 50) {
                html = '[20-50]';
            } else if (negativeGrossProfitNum >= 50 && negativeGrossProfitNum < 100) {
                html = '[50-100]';
            } else if (negativeGrossProfitNum >= 100 && negativeGrossProfitNum < 200) {
                html = '[100-200]';
            } else if (negativeGrossProfitNum >= 200 && negativeGrossProfitNum < 400) {
                html = '[200-400]';
            } else if (negativeGrossProfitNum >= 400 && negativeGrossProfitNum < 600) {
                html = '[400-600]';
            } else if (negativeGrossProfitNum >= 600) {
                html = '[600及以上]';
            }
        }

        return html
    }

    /**
     * 校验车牌号和司机必填
     */
    function validateRequiredFields() {
        var hasError = false;
        var errorMessages = [];

        for (let i = 0; i < affirmVOList.length; i++) {
            var carrSelect = $(`#carr_${i}`);
            if (carrSelect.length > 0) {
                var selectedOption = carrSelect.find('option:selected');
                var requiredFlag = selectedOption.data('requiredflag') || 4;
                var carno = $(`#carno_${i}`).val();
                var driverName = $(`#driverName_${i}`).val();
                var vbillno = affirmVOList[i].vbillno || `第${i+1}条`;
                let isAutoDispatch = $("#isAutoDispatch_" + i).val();

                if (isAutoDispatch == 0) {
                    continue;
                }
                if (requiredFlag === 1) {
                    // 车辆司机必填
                    if (!carno || carno.trim() === '') {
                        hasError = true;
                        errorMessages.push(`发货单${vbillno}：车牌号为必填项`);
                    }
                    if (!driverName || driverName.trim() === '') {
                        hasError = true;
                        errorMessages.push(`发货单${vbillno}：司机为必填项`);
                    }
                } else if (requiredFlag === 2) {
                    // 车辆必填
                    if (!carno || carno.trim() === '') {
                        hasError = true;
                        errorMessages.push(`发货单${vbillno}：车牌号为必填项`);
                    }
                } else if (requiredFlag === 3) {
                    // 司机必填
                    if (!driverName || driverName.trim() === '') {
                        hasError = true;
                        errorMessages.push(`发货单${vbillno}：司机为必填项`);
                    }
                }
            }
        }

        if (hasError) {
            $.modal.alertWarning(errorMessages.join('<br>'));
            return false;
        }
        return true;
    }


    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form() && validateRequiredFields()) {
            var data = $("#form-out-quote").serializeArray();
            $.operate.save(ctx + "tms/segment/autoDis", data);
        }
    }


    /**
     * 编辑应付金额
     * @param index 索引
     * @param originalAmount 原始金额
     */
    function editPayTotalFee(index, originalAmount) {
        // 当前分拆默认值
        var oilVal = $(`#oilAmount_${index}`).val() || 0;
        var cashVal = $(`#cashAmount_${index}`).val() || 0;

        var contentHtml = `
        <div style="padding: 15px 20px;">
            <div style="margin-bottom: 12px;">
                <label style="width: 70px; display: inline-block;">油卡：</label>
                <div style="position: relative; display: inline-block;">
                    <input id="editOilAmount_${index}" class="form-control" style="width: 160px; padding-right: 25px;"
                           value="${oilVal}">
                    <span class="clear-btn" data-target="editOilAmount_${index}" style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #999; font-size: 14px;" title="清空">×</span>
                </div>
            </div>
            <div>
                <label style="width: 70px; display: inline-block;">现金：</label>
                <div style="position: relative; display: inline-block;">
                    <input id="editCashAmount_${index}" class="form-control" style="width: 160px; padding-right: 25px;"
                           value="${cashVal}">
                    <span class="clear-btn" data-target="editCashAmount_${index}" style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #999; font-size: 14px;" title="清空">×</span>
                </div>
            </div>
            <div style="margin-top:10px;color:#999;">应付上限：${formatCurrency(originalAmount)}（油卡+现金需小于该值）</div>
        </div>`;

        layer.open({
            type: 1,
            title: '编辑议价后金额',
            area: ['360px','230px'],
            shade: 0.3,
            content: contentHtml,
            btn: ['保存','取消'],
            success: function(layero, layerIndex) {
                // 限制油卡金额的函数
                function limitOilAmount() {
                    var oilInput = layero.find(`#editOilAmount_${index}`);
                    var cashInput = layero.find(`#editCashAmount_${index}`);

                    var oilVal = parseFloat(oilInput.val()) || 0;
                    var cashVal = parseFloat(cashInput.val()) || 0;

                    // 计算油卡最大可输入金额
                    var maxOilAmount = originalAmount - cashVal;

                    // 如果油卡输入超过最大可输入金额，自动调整为最大值
                    if (oilVal > maxOilAmount) {
                        oilInput.val(maxOilAmount >= 0 ? maxOilAmount.toFixed(2) : '0.00');
                    }
                }

                // 限制现金金额的函数
                function limitCashAmount() {
                    var oilInput = layero.find(`#editOilAmount_${index}`);
                    var cashInput = layero.find(`#editCashAmount_${index}`);

                    var oilVal = parseFloat(oilInput.val()) || 0;
                    var cashVal = parseFloat(cashInput.val()) || 0;

                    // 计算现金最大可输入金额
                    var maxCashAmount = originalAmount - oilVal;

                    // 如果现金输入超过最大可输入金额，自动调整为最大值
                    if (cashVal > maxCashAmount) {
                        cashInput.val(maxCashAmount >= 0 ? maxCashAmount.toFixed(2) : '0.00');
                    }
                }

                // 绑定油卡输入事件
                layero.find(`#editOilAmount_${index}`).on('input', function() {
                    // 数字格式化
                    if ($.numberUtil && $.numberUtil.onlyNumberTwoDecimal) {
                        $.numberUtil.onlyNumberTwoDecimal(this);
                    }
                    limitOilAmount();
                });

                // 绑定现金输入事件
                layero.find(`#editCashAmount_${index}`).on('input', function() {
                    // 数字格式化
                    if ($.numberUtil && $.numberUtil.onlyNumberTwoDecimal) {
                        $.numberUtil.onlyNumberTwoDecimal(this);
                    }
                    limitCashAmount();
                });

                // 绑定清空按钮事件
                layero.find('.clear-btn').on('click', function() {
                    var targetId = $(this).data('target');
                    var targetInput = layero.find('#' + targetId);
                    targetInput.val('').focus();

                    // 清空后重新计算限制
                    if (targetId.indexOf('Oil') > -1) {
                        limitOilAmount();
                    } else {
                        limitCashAmount();
                    }
                });
            },
            yes: function(layerIndex, layero){
                var oilInput = layero.find(`#editOilAmount_${index}`).val();
                var cashInput = layero.find(`#editCashAmount_${index}`).val();

                var oil = toDecimal(oilInput || 0);
                var cash = toDecimal(cashInput || 0);

                if (oil.lt(0) || cash.lt(0)) {
                    layer.msg('金额不能为负数', {icon: 2});
                    return false;
                }

                var sum = oil.plus(cash);
                var cap = toDecimal(originalAmount || 0);

                // 需小于上限
                if (!sum.lte(cap)) {
                    layer.msg('油卡+现金之和必须小于应付金额', {icon: 2});
                    return false;
                }

                // 回填隐藏字段（保留两位小数）
                $(`#oilAmount_${index}`).val(oil.toFixed(2));
                $(`#cashAmount_${index}`).val(cash.toFixed(2));
                // 更新应付隐藏字段为分拆合计
                $(`#payTotalFee_${index}`).val(sum.toFixed(2));
                // 更新页面显示的应付金额为分拆合计
                $(`#payTotalFeeText_${index}`).html(formatCurrency(sum.toFixed(2)));

                // 重新计算
                calculateProfit(index);

                layer.close(layerIndex);
            }
        });
    }

    /**
     * 保存修改后的应付金额
     * @param index 索引
     * @param originalAmount 原始金额
     */
    function savePayTotalFee(index, originalAmount) {
        // 已切换为弹窗双输入，不再使用该函数体，保留兼容调用
        editPayTotalFee(index, originalAmount);
    }

    




</script>
</body>

</html>