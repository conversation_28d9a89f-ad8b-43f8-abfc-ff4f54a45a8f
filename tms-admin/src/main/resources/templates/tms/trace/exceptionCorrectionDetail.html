<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('整改')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .cur{
        cursor: pointer;
    }
    .select{

    -webkit-user-select:none;

    -moz-user-select:none;

    -ms-user-select:none;

    user-select:none;

    }
    .fcff{
        color: #ff1f1f;
    }
    .file-drop-zone{
        overflow: auto;
    }
    .switch{
        width:40px;
        height:24px;
        border-radius:16px;
        overflow: hidden;
        vertical-align:middle;
        position:relative;
        display: inline-block;
        background:#ccc;
        box-shadow: 0 0 1px #1ab394;
    }
    .switch input{
        visibility: hidden;
    }
    .switch span{
        position:absolute;
        top:0;
        left:0;
        border-radius: 50%;
        background:#fff;
        width:50%;
        height:100%;
        transition:all linear 0.2s;
    }
    .switch span::before{
        position: absolute;
        top:0;
        left:-100%;
        content:'';
        width:200%;
        height:100%;
        border-radius: 30px;
        background:#1ab394;
    }
    .switch span::after{
        content:'';
        position:absolute;
        left:0;
        top:0;
        width:100%;
        height:100%;
        border-radius: 50%;
        background:#fff;
    }
    .switch input:checked +span{
        transform:translateX(100%);
    }
    .f16 {
        font-size: 16px;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;
    }

    .flex_left {
        width: 70px;
        line-height: 24px;
        text-align: right;
    }

    .flex_right {
        min-width: 12em;
        flex: 1;
        line-height: 24px;
        margin-left: 5px;
    }

    .over {
        overflow: hidden;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }
    .ml10 {
        margin-left: 10px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .fw {
        font-weight: bold;
    }

    .ydbox {
        padding: 10px 20px;
        border: 1px #eee solid;

    }

    .ydcontent {
        background: #7f7f7f;
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .ydcontent_title {
        padding: 5px 10px;
        color: #fff;
    }

    .ydcontent_box {
        padding: 10px 10px;
        border-radius: 10px;
        min-height: 60px;
        box-sizing: border-box;
        background: #fff;
        /*border: 1px #7f7f7f solid;*/
        box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.08), 0px 4px 10px 4px rgba(0, 0, 0, 0.08)
    }

    .fc80 {
        color: #808080;
    }

    .addbtn {
        background: #1ab394;
        color: #fff;
        cursor: pointer;
        width: 120px;
        text-align: center;
        line-height: 30px;
    }

    [class^=dhlist] {
        border: 1px #eee solid;
        width: 100%;
        padding: 3px 0;
    }

    [class^=dhlist] span {
        display: inline-block;
        background: #f3f3f3;
        line-height: 20px;
        padding: 0 5px;
        margin-left: 5px;
        margin-bottom: 5px;
    }

    .table-title {
        text-align: center;
        font-weight: bold;
        line-height: 24px;
        background: #f7f8fa
    }

    .tablebox {
        border: 1px #333333 solid;
    }

    .cys {
        padding: 10px 0;
        border-bottom: 1px #9f9f9f dashed;
    }

    .fc33 {
        color: #f33131;
    }

    .fc1a {
        color: #1ab394;
    }

    .bor33 {
        border: 1px #f33131 solid;
    }

    .bor1a {
        border: 1px #1ab394 solid;
    }

    .insurance {
        background: #f8f9fc;
        width: 100%;
    }

    .insurancebox {
        padding: 5px 5px;
    }

    .table_text {
        width: 100%;
        text-align: center;
    }

    .boree {
        border: 1px #eee solid;
        box-sizing: border-box;
        line-height: 20px;
    }

    .tabel_total {
        background: #fffcd3;
        padding: 5px 20px;

    }

    .file-input-ajax-new .file-drop-zone-title {
        /*height: 80px;*/
    }

    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }

    .theme-explorer .file-preview .table tr {
        border-bottom: 1px #dadada dashed;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }

    .eye .file-drop-zone-title {
        background: url('../../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }

    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242, 222, 222, 0.9);
        text-align: center;
        line-height: 70px;
    }

    .file-error-message button span {
        line-height: 70px;
    }

    .file-error-message li {
        text-align: center;
    }
    .boxs{
        box-shadow: 0px 2px 5px 0px rgb(0 0 0 / 8%), 0px 4px 10px 4px rgb(0 0 0 / 8%);
        border-radius: 10px;
    }
    .boxw{
        padding: 5px 0;
    }
    .boxw .flex_left{
        line-height: 26px;
    }
    .fc1ab{
        color: #1ab394;
    }
    .mar10{
        margin-right: 10px;
        margin-top: 10px;
    }
    .del_btn{
        background: #ed5565;
        color: #fff;
        width: 100px;
        line-height: 30px;
        text-align: center;
        height: 30px;
        border-radius: 3px;
    }
    .table>thead>tr>th {
        font-weight: normal;
    }
    .th_img{
        width: 100px;
        height: 100px;
        object-fit: scale-down;
    }
    .bxpf{
        width: 100%;
    }
    @media screen and (min-width: 1024px) {
        .widt10 {
            width: 10%;
        }
        .widt20 {
            width: 20%;
        }
        .widt25 {
            width: 25%;
        }
    }
    @media screen and (max-width: 1024px) {
        .widt10 {
            width: 20%;
        }
        .widt20 {
            width: 33.33333%;
        }
        .widt25 {
            width: 33.33333%;
        }

    }

    .bgf7{
        background: #f7f8fa;
        padding: 5px 5px;
    }

    .selloutBj{
        position: relative;
        overflow: hidden;
    }
    .sellout {
        background-color: #1c84c6;
        color: #fff !important;
        width: 30%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        right: -8%;
        top: 5%;
        transform: rotate(38deg);
        font-size: 14px;
    }
    body{
        background-color: #e7eaec;
    }
    .form-content{
        background-color: transparent;
        padding-bottom: 60px;
    }
    .white-bg {
        padding: 10px 15px;
        border-radius: 5px;
    }
    .toBut{
        vertical-align: middle;
    }
    .f18{
        font-size: 18px;
    }
    .btnT{
        border-radius: 50px;
        padding: 3px 10px;
    }
    .flex{
        align-items: center;
    }
    .taTX{
        min-width: 5em;
    }
    .taTX>div:first-child{
        height: 24px;
        line-height: 24px;
    }
    .naTx{
        margin-top: 5px;
        padding: 5px 0;
        border-top: 1px solid #e7eaec;
        border-bottom: 1px solid #e7eaec;
        text-align: center;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .form-horizontal .radio-inline{
        padding-top: 0;
    }
    .f14{
        font-size: 15px;
    }
    .bTit{
        background-color: rgba(237, 41, 41, 0.05);
        padding:0 5px;
        border: 1px solid rgba(237, 41, 41, 0.08);
        color: #420000;
        font-size: 13px;
        padding: 5px;
        border-radius: 5px;
    }
    .no-gutter{
        margin-left: -10px;
        margin-right: -10px;
    }
    .lh20{
        line-height: 20px;
        display: inline-block;
    }
    .tabr{
        border: 1px dashed;
        padding: 5px 15px;
        border-radius: 5px;
        display: inline-block;
    }
    .label-dangerT{
        color: #ed5565;
        border: 1px solid #ed5565;
        background-color: #fff;
    }
    .label-primaryT{
        color: #1ab394;
        border: 1px solid #1ab394;
        background-color: #fff;
    }
    .label-defaultT{
        color: #5e5e5e;
        border: 1px solid #5e5e5e;
        background-color: #fff;
    }
</style>
<body>
<div class="form-content">
    <form id="form-adnormal-add" class="form-horizontal" novalidate="novalidate">
        
        <div class="white-bg">
            <div class="flex" style="justify-content: space-between;">
                <div style="width: 50%;">
                    <div class="flex">
                        <img th:src="@{/img/hu.png}" style="width: 60px;height: 60px;"/>
                        <div class="ml5">
                            <div class="toBut">
                                <span class="toBut f18 fw" th:text="${invoiceReceiveDetail.custAbbr}"></span>
                                <span class="ml5 toBut">
                                    <span class="label label-defaultT" th:if="${entrustExp.caseStatus == '1'}">新建</label>
                                    <span class="label label-dangerT" th:if="${entrustExp.caseStatus == '2'}">处理中</label>
                                    <span class="label label-primaryT" th:if="${entrustExp.caseStatus == '3'}">已结案</label>
                                </span>
                            </div>
                            <div class="toBut mt5 flex">
                                <div>
                                    <span>托运单号：</span><span th:text="${invoiceReceiveDetail.vbillno}"></span>
                                </div>
                                <div style="margin-left: 20px;">
                                    <span>提报日期:</span><span th:text="${ #dates.format(entrustExp.accidentTime, 'yyyy-MM-dd') }"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt10">
                        <div>
                            <span class="label label-warning pa2">装</span> 
                            <span class="ml5" th:text="${invoiceReceiveDetail.shippingAddress}"></span>
                        </div>
                        <div>
                            <span class="label label-success pa2">卸</span> 
                            <span class="ml5" th:text="${invoiceReceiveDetail.receivingAddress}"></span>
                        </div>
                    </div>

                    <div class="flex mt10" style="justify-content: space-between;">
                        <div style="background-color: #F4F5F7;padding: 8px;">
                            <div class="flex">
                                <img th:src="@{/img/wp.png}" style="width: 20px;height: 20px;"/>
                                <span class="lh20" th:text="${invoiceReceiveDetail.goodsName}"></span>
                                <span class="ml5 lh20" th:text="${invoiceReceiveDetail.weightCount+'吨'}"></span>
                            </div>
                        </div>
                    </div>


                </div>
                <div class="boxw" style="max-width: 50%;">
                    <div class="bTit">
                        <span th:each="item,status:${#strings.arraySplit(entrustExp.expType,',')} " th:if="${entrustExp.expType != null}">
                            <span class="f18 fw" th:each="dict: ${@dict.getType('exception_type')}" th:if="${item==#strings.toString(dict.dictCode)}">
                                <span th:text="${dict.dictLabel}"></span>
                            </span>
                        </span>
                        <div class="flex boxw">
                            <div class="flex_left">事件简述:</div>
                            <div class="flex_right" th:text="${entrustExp.handleNote}"></div>
                        </div>  
                    </div>
                        <div class="flex boxw" style="align-items: flex-start;">
                        <div class="flex_left">附件照片:</div>
                        <div class="flex_right picviewer">
                            <div style="display:inline-block" th:each="mapS,status:${files}">
                                <img class="th_img" src="" th:src="${mapS.filePath}"/>
                            </div>
                        </div>
                    </div>  
                </div>
            </div>
        </div>

        <div class="white-bg mt10">
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">业务员:</div>
                        <div class="flex_right" th:text="${invoiceReceiveDetail.deptName}"></div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">运营部:</div>
                        <div class="flex_right" th:text="${invoiceReceiveDetail.salesName}"></div>
                    </div>
                </div>
            </div>

            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">责任人:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.responsiblePerson}"></div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">责任小组:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.responsibilityTeam}"></div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">现场:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.onSite}"></div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">部门负责人:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.manager}"></div>
                    </div>
                </div>
            </div>
            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left"></div>
                        <div class="flex_right">
                            <div class="tabr cur select" onclick="onExamine(this)">
                                <span class="text-success">考核人员</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

             <div style="display: none;" th:unless="${ exceptionCorrection == null }">
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="flex boxw" style="align-items: flex-start;">
                            <div class="flex_left">考核人员:</div>
                            <div class="flex_right">
                                <div class="entrusts">
                                    <div th:class="${status.index ==0 ?'row no-gutter':'row no-gutter mt10'}" th:each="dict,status: ${exceptionCorrection.appraisersList}">
                                        <div class="col-md-6 col-sm-6">
                                            <span th:text="${dict.name}"></span> - 
                                            <span th:text="${dict.amount}"></span>
                                            <span th:text="${dict.unit==1?'金额':'分'}"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="flex boxw" style="align-items: flex-start;">
                            <div class="flex_left">备注:</div>
                            <div class="flex_right" th:text="${exceptionCorrection.remark}"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-12 col-sm-12">
                    <span class="f18 fw">根本原因分析:</span>
                </div>
                <div class="col-md-12 col-sm-12 mt10">
                    <div>
                        <textarea name="rootCauseAnalysis" maxlength="200" class="form-control" rows="5" th:text="${ exceptionCorrection==null?'':exceptionCorrection.rootCauseAnalysis }" disabled></textarea>
                    </div>
                    <div class="picviewer mt10">
                        <div style="display:inline-block" th:each="mapS,status:${analysisFiles}">
                            <img style="width: 100px;" src="" th:src="${mapS.filePath}"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-12 col-sm-12">
                    <span class="f18 fw">改善对策:</span>
                </div>
                <div class="col-md-12 col-sm-12 mt10">
                    <div>
                        <textarea name="improvementMeasures" maxlength="200" class="form-control" rows="5" th:text="${ exceptionCorrection==null?'':exceptionCorrection.improvementMeasures }" disabled></textarea>
                    </div>
                    <div class="picviewer mt10">
                        <div style="display:inline-block" th:each="mapS,status:${measuresFiles}">
                            <img style="width: 100px;" src="" th:src="${mapS.filePath}"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10 white-bg">
            <!-- <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button> -->
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
            </button>
        </div>
    </div>
</div>
</form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    $(function () {
        $('.picviewer').viewer({
            url: 'data-original',
            title: false,
            navbar:false,
        });
    });
    function onExamine(obj) {
        let display=$(obj).parents(".row").next().css("display");
        if(display=='none'){
            $(obj).parents(".row").next().css('display','block');
        }else{
            $(obj).parents(".row").next().css('display','none');
        }
    }
</script>
</body>

</html>