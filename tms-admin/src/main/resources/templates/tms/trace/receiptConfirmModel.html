<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('回单')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <style>
        td{
            position:relative
        }
        label.error{
            top:10px !important;
        }
        .fontLeft{
            text-align: left;
        }
        .fontRight{
            text-align:right;
        }
        .fw{
            font-weight: 600;
        }

        .vertical-timeline-icon{
            width: 3em;
            height: 3em;
            color: #fff;
            left: calc(50% - 15px);
            top: -14px;
            font-size: 12px;
            background-color: #18a689;
            border-color: #18a689;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }

        .vertical-timeline-block{
            margin: 2em 0 !important;
        }
        .f18a{
            color: #18a689;
        }
        .vtb-tit {
            width: 6em;
            height: 20px;
            line-height: 20px;
            text-align: center;
            position: absolute;
            left: calc(50% - 2.5em);
            top: -30px;
            z-index: 1;
        }

        .vertical-timeline-block{
            position: relative;
        }
        .vertical-timeline-block::after{
            content:" ";
            position: absolute;
            /* width: calc(100% + 80px); */
            width: 100%;
            height: 0;
            border-bottom:#18a689 2px dashed;
            top: 4px;
            left: 0;
        }

        .vertical-timeline-content{
            width: 200px;
            /* text-align: center; */
            margin-left: 0;
            left: calc(50% - 88px);
            top: 20px;
        }
        /* .vertical-timeline-content .over{
            position: relative;
            left: 15%;
        } */
        .trans_box{
            width: 100%;
            text-align: center;
            position: absolute;
            top: -70px;
            left: calc(50% - 106px);
            /* color: #18a689; */
        }
        .th_img{
            width: 20px;
            height: 20px;
        }
        .carrierInfo{
            width: 100px;
            left: calc(100% - 50px);
            /* height: 20px; */
            line-height: 20px;
            text-align: center;
            position: absolute;
            top: -16px;
            z-index: 1;
        }
        .carrierInfo span{
            display: inline-block;
        }
        .vertical-container{
            width: 100%;
            max-width: initial;
        }
        .light-timeline+.light-timeline{
            border-top: 2px #EBECEF solid;
            margin-top: 15px;
            padding-top: 15px;
        }
        .timeBor{
            height: 50px;
            border: #ddd 1px solid;
            border-top: 0;
        }
        .timeBorF{
            border-color: #ffffff;
        }
        .timeBor+.timeBor{
            border-left: 0;
        }
        .timeBor span{
            display: inline-block;
            margin-top: 40px;
            color: #ff1f1f;
            background-color: #ffffff;
            padding: 0 5px;
        }
        .panel-body{
            padding: 0 5px 0;
        }
        .fl{
            display: inline-block; 
        }
        .btn-info{
            background-color: #18a689;
            border-color: #18a689;
        }
        .btn-danger{
            background-color: transparent;
            border-color: #ec4758;
            color: #ec4758;
        }
        .file-drop-zone{
            overflow-x: auto;
        }
        .dictImg{
           display:inline-block;padding: 5px;height:80px;width: 80px;box-sizing: border-box;
        }
        .dictImg img{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-receipt-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel panel-default" style="display: none">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">回单货品信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--订单货品费用明细 begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">货品编码</th>
                                <th style="width: 12%;">货品名称</th>
                                <th style="width: 10%;">回单件数</th>
                                <th style="width: 10%;">回单重量(吨)</th>
                                <th style="width: 10%;">回单体积(m3)</th>
                                <th style="width: 10%;">货物照片</th>
                                <!--                                    <th style="width: 10%;">操作</th>-->
                            </tr>
                            </thead>
                            <tbody>
                            <tr name="regRows" th:each="goods,goodsStat:${entPackGoodsList}">
                                <td style="text-align:left" th:text="${goods.goodsCode}"></td>
                                <td style="text-align:left" th:text="${goods.goodsName}"></td>
                                <input name="goodsId" th:value="${goods.entPackGoodsId}" type="hidden">
                                <td><input name="receiptNum" class="form-control fontRight" oninput="$.numberUtil.onlyNumberNotNull(this)" type="text" th:value="${goods.num}"></td>
                                <td><input name="receiptWeight" class=" form-control fontRight" oninput="$.numberUtil.onlyNumberNotNull(this)" type="text" th:value="${goods.weight}"></td>
                                <td><input name="receiptVolume" class=" form-control fontRight" oninput="$.numberUtil.onlyNumberNotNull(this)" type="text" th:value="${goods.volume}"></td>
                                <!--                                    <td><a th:if="${goodsStat.index==0}" class="btn btn-warning btn-rounded btn-xs" onclick="cargo()"><i class="fa fa-pencil"></i>&nbsp;货量更新</a></td>-->
                                <td th:rowspan="${goodsStat.size}">
                                    <div class="picviewer">
                                                    <span th:each="dict:${pic}" th:if="${dict.context == '货物照片'}">
                                                        <span style="display:inline-block" th:each="pic:${picList}" th:if="${pic.workAppendixType==dict.value and pic.filePath!=null}">
                                                            <img class="th_img" th:src="@{${pic.filePath}}"/>
                                                        </span>
                                                    </span>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--订单货品费用明细 end-->
                </div>
            </div>
        </div>

        <div class="row" style="margin-left: 5px;margin-right: 5px">

            <div class="col-sm-12">
                <input name="entrustId" id="entrustId" type="hidden" th:value="${entrustDto.entrustId}">
                <div class="row">
                    <div class="col-md-12 col-sm-12" th:each="dict:${pic}" th:if="${dict.context == '回单照片'}">
                        <div class="form-group">
                            <label class="col-md-12 col-sm-12" th:text="${dict.context}+'：'"></label>
                            <div class="col-xs-12">
                                <div class="dictImg" th:each="pic:${picList}"
                                     th:if="${pic.workAppendixType==dict.value and pic.filePath!=null}">
                                    <img modal="zoomImg" th:src="@{${pic.filePath}}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt10" style="height: 27px" th:if="${tid != null && tid != ''}">
                    <div class="col-xs-12">
                        <a class="btn btn-danger btn-xs" onclick="removePic()">删除回单照片</a>
                    </div>
                </div>

                <div class="row mt5" th:if="${tid == null || tid == ''}">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-12">
                            回单照片补录：
                            </label>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <div class="col-xs-12" >
                                <input id="receipt" name="receipt"  class="form-control" type="file" multiple>
                                <input th:value="${tid}" name="tid" id="tid" type="hidden" >
                            </div>
                        </div>

                    </div>
                    <a class="btn btn-info btn-xs" onclick="commitPic()">确认上传</a>
                </div>

                
            </div>
        </div>
       


    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "trace";

    var entrustId = [[${entrustDto.entrustId}]];
    var orderno = [[${entrustDto.orderno}]];
    var ltlType = [[${entrustDto.ltlType}]];

    var isFleetData = [[${isFleetData}]]
    var isFleetAssign = [[${isFleetAssign}]]

    var receiptDate =[[${#dates.format(entrustDto.receiptDate,'yyyy-MM-dd HH:mm:ss')}]];
    var receiptErrorFlag = [[${receiptErrorFlag}]];

    //图片
    var sysUploadFiles = [[${sysUploadFiles}]];
    //委托单作业id
    var entrustWorkId = [[${entrustWork.entrustWorkId}]];



    function submitHandler() {
        commit();
    }

    function commit(){
        if(ltlType != 0){
            if(receiptErrorFlag == "1"){
                $.modal.alertWarning("该委托单到货作业未上传回单照片，无法进行回单确认，请先上传回单照片")
                return false;
            }
        }
        if ($.validate.form()) {

            $.modal.confirm("一旦确认应付费用将锁定，确定回单确认吗？", function () {
                var data = {};
                data.entrustId = $("#entrustId").val();
                data.receiptMan = $("#receiptMan").val();
                data.receiptDate = $("#receiptDate").val();
                data.tid = $("#tid").val();
                data.receiptMemo = $("#receiptMemo").val();
                data.custOrderno = $("#custOrderno").val();
                data.receiptNum = $("#receiptNum").val();
                data.entrustWorkId = entrustWorkId;
                //遍历货品
                var rows = [];
                $("tr[name='regRows']").each(function () {
                    var row = {};
                    var goodsId = $(this).find("input[name='goodsId']").val();
                    var receiptNum = $(this).find("input[name='receiptNum']").val();
                    var receiptWeight = $(this).find("input[name='receiptWeight']").val();
                    var receiptVolume = $(this).find("input[name='receiptVolume']").val();

                    row.entrustId = entrustId;
                    row.goodsId = goodsId;
                    row.receiptNum = receiptNum;
                    row.receiptWeight = receiptWeight;
                    row.receiptVolume = receiptVolume;
                    rows.push(row);
                });
                data.goodsList = rows;

                $.operate.saveTabJson(prefix + "/addReceiptConfirm", data, function (result) {
                    if (result.code == web_status.SUCCESS) {
                        parent.location.reload();
                    }
                });

                /*let isRefresh = [[${isRefresh}]]
                if (isRefresh) {

                }else {
                    $.operate.saveTabJson(prefix + "/addReceiptConfirm", data, function (result) {
                        if (result.code == web_status.SUCCESS) {
                            parent.$.table.refresh()
                        }
                    });

                }*/

                // $.operate.saveTabJson(prefix + "/addReceiptConfirm", data);

            });
        }
    }



    $(function () {


        var options = {};
        $.table.init(options);

        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: null
        };
        $.file.loadEditFiles("receipt", "tid", sysUploadFiles, picParam);


        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#receiptDate', //指定元素
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                },
                value: receiptDate,
                done: function(value, date, endDate){
                    $("#receiptDate").val(value);
                    $("#form-receipt-add").validate().element($("#receiptDate"))
                }
            });
        });

    });



    function commitPic(){
        $.modal.confirm("是否确认上传图片？", function () {
            $('#receipt').fileinput('upload');
            jQuery.subscribe("cmt", commit1);
        });
    }

    function removePic() {
        $.modal.confirm("是否删除所有回单照片？", function () {
            var data = {};
            data.entrustId = $("#entrustId").val();
            data.entrustWorkId = entrustWorkId;
            $.operate.saveModalNoCloseAndRefush(prefix + "/removePic", data,function(result){
                if(result.code == 0){
                    //alert(1);
                    //window.location.reload()
                }
            });
        });
    }

    function commit1() {
        var data = {};
        data.entrustId = $("#entrustId").val();
        data.tid = $("#tid").val();
        data.entrustWorkId = entrustWorkId;
        $.operate.saveModalNoCloseAndRefush(prefix + "/savePic", data,function(result){
            if(result.code == 0){
                //alert(1);
                //window.location.reload()
            }
        });
    }
</script>
</body>

</html>