<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增异常跟踪')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .checkbox{
        padding-top: 0 !important;
    }
    .checkbox input[type="radio"] {
        position: absolute;
        clip: rect(0, 0, 0, 0);
    }
 
    .checkbox input[type='radio'] + label {
        display: block;
        height: 26px;
        padding: 6px 12px;
        font-size: 13px;
        font-weight: 500;
        line-height: 1;
        border: 1px solid #ccc;
        text-align: center;
        float: left;
        margin-right: 10px;
        cursor: pointer;
        border-radius: 2px;
    }
 
    .checkbox input[type='radio']:checked + label {
        border: 1px solid #009aff;
        color: #009aff;
        border-radius: 2px;
        font-weight: 500;
    }


    .fcff{
        color: #ff1f1f;
    }
    .switch{
        width:40px;
        height:24px;
        border-radius:16px;
        overflow: hidden;
        vertical-align:middle;
        position:relative;
        display: inline-block;
        background:#ccc;
        box-shadow: 0 0 1px #1ab394;
    }
    .switch input{
        visibility: hidden;
    }
    .switch span{
        position:absolute;
        top:0;
        left:0;
        border-radius: 50%;
        background:#fff;
        width:50%;
        height:100%;
        transition:all linear 0.2s;
    }
    .switch span::before{
        position: absolute;
        top:0;
        left:-100%;
        content:'';
        width:200%;
        height:100%;
        border-radius: 30px;
        background:#1ab394;
    }
    .switch span::after{
        content:'';
        position:absolute;
        left:0;
        top:0;
        width:100%;
        height:100%;
        border-radius: 50%;
        background:#fff;
    }
    .switch input:checked +span{
        transform:translateX(100%);
    }
    .f16 {
        font-size: 16px;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;
    }

    .flex_left {
        /* width: 80px; */
        line-height: 24px;
        text-align: right;
    }

    .flex_right {
        min-width: 7em;
        flex: 1;
        line-height: 24px;
    }

    .mt10 {
        margin-top: 10px;
    }

    .fw {
        font-weight: bold;
    }

    [class^=dhlist] span {
        display: inline-block;
        background: #f3f3f3;
        line-height: 20px;
        padding: 0 5px;
        margin-left: 5px;
        margin-bottom: 5px;
    }

    .mt10 {
        margin-top: 10px;
    }

    .kv-upload-progress .progress {
        display: none;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .uploads .table-bordered td{
        border: 0 !important;;
    }

    .boxs{
        box-shadow: 0px 2px 5px 0px rgb(0 0 0 / 8%), 0px 4px 10px 4px rgb(0 0 0 / 8%);
        border-radius: 10px;
    }
    .boxw{
        padding: 5px 0;
    }
    .boxw .flex_left{
        line-height: 26px;
    }

    .table>thead>tr>th {
        font-weight: normal;
    }

    body{
        background-color: #e7eaec;
    }
    .form-content{
        background-color: transparent;
        padding-bottom: 60px;
    }
    .white-bg {
        padding: 10px 15px;
        border-radius: 5px;
    }
    .flex{
        align-items: center;
    }
    .taTX{
        min-width: 5em;
    }
    .taTX>div:first-child{
        height: 24px;
        line-height: 24px;
    }
    .form-horizontal .radio-inline{
        padding-top: 0;
    }
    .f14{
        font-size: 15px;
    }
    .bTit{
        background-color: #FFE0E0;
        padding: 0 5px;
        border: 1px dashed #ED2929;
        color: #530F0F;
        font-size: 13px;
    }

    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
</style>
<body>
<div class="form-content">
    <form id="form-adnormal-add" class="form-horizontal" novalidate="novalidate">
        <input id="isCustomerException" name="isCustomerException" type="hidden" th:value="${entrustExp.isCustomerException}"/>
        <div class="row" style="padding: 0 5px;">
            <div class="col-md-12 col-sm-12">
                <div class="flex boxw bTit">
                    <div class="flex_left">标题-</div>
                    <div class="flex_right" id="exceptionTitle" th:text="${entrustExp.exceptionTitle}"></div>
                </div>
            </div>
        </div>

        <div class="white-bg mt10">
            <div class=" f14 fw text-danger"> 异常类型基本信息 </div>
            <div>
                <label class="checkbox-inline" th:each="dict : ${@dict.getType('exception_type')}">
                    <input type="checkbox" name="expType" th:value="${dict.dictCode}" th:text="${dict.dictLabel}" onclick="onExceptionTitle()"
                           th:checked="${entrustExp.expType != null?#arrays.contains(#strings.arraySplit(entrustExp.expType,','),#strings.toString(dict.dictCode)):'false'}"
                           required="true" aria-required="true">
                    <i class="fa fa-question-circle-o" data-toggle="tooltip" style="font-size: 15px" data-html="true" data-container="body" th:title="${dict.remark}" th:if="${dict.remark != null}"></i>
                </label>
            </div>
            <label id="expType-error" class="error" for="expType"></label>
            

            <div class="row mt10">
                <div class="col-md-2 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left"><span class="fcff">*</span>异常时间：</div>
                        <div class="flex_right">
                            <input type="text" class="form-control" th:value="${#dates.format(entrustExp.accidentTime, 'yyyy-MM-dd')}" onblur="onExceptionTitle()" name="accidentTime" id="accidentTime" placeholder="请选择时间" readonly  required="true" aria-required="true">
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6">
                    <div class="flex boxw">
                        <label class="flex_left"><span class="fcff">*</span> 客户简称：</label>
                        <div class="flex_right">
                            <!-- <div class="input-group"> -->
                            <input name="custAbbr" id="custAbbr" onclick="selectClient()" type="text"  placeholder="请选择客户" class="form-control valid" aria-required="true" required autocomplete="off" th:value="${customer.custAbbr}">
                            <input name="custName" id="custName" type="hidden">
                            <input name="customerId" id="customerId" type="hidden" th:value="${customer.customerId}">
                        </div>
                    </div>
                </div>

                <div class="col-md-2 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">初步损失情况：</div>
                        <div class="flex_right">
                            <input type="text" id="preliminaryLoss" class="form-control" th:value="${entrustExp.preliminaryLoss}">
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-sm-12">
                    <div class="flex boxw" style="align-items: flex-start;">
                        <div class="flex_left"><span class="fcff">*</span>简要说明：</div>
                        <div class="flex_right">
                            <textarea id="handleNote" name="handleNote" maxlength="200" class="form-control" rows="3" th:text="${entrustExp.handleNote}" required="true" aria-required="true"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">是否报保险：</div>
                        <div class="flex_right">
                            <label class="radio-inline">
                                <input type="radio" name="isInsurance" value="1" th:checked="${entrustExp.isInsurance == 1}"> 是
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="isInsurance" value="0" th:checked="${entrustExp.isInsurance == 0}"> 否
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6" id="reportNoDiv" style="display: none;">
                    <div class="flex boxw">
                        <div class="flex_left">报案号：</div>
                        <div class="flex_right">
                            <input type="text" id="reportNo" class="form-control" required="false" th:value="${entrustExp.reportNo}"
                                   aria-required="false">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="white-bg mt10">
            <div class=" f14 fw"> 跟踪记录 </div>
            <div class="row mt10">
                <div class="col-md-4 col-sm-12">
                    <div class="boxw" style="align-items: flex-start;">
                        <div>现场照片：</div>
                        <div class="mt5">
                            <input id="image" class="form-control" name="image" type="file" multiple>
                            <input id="appendixId" name="appendixId" type="hidden" th:value="${entrustExp.appendixId}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt10">
                <table class="table table-bordered" id="tracetab">
                    <thead style="background:#f7f8fa">
                    <tr>
                        <th style="width: 8%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowTrace()" title="新增行">+</a></th>
                        <th style="width: 20%">跟踪日期</th>
                        <th style="width: 40%">内容</th>
                        <th style="width: 40%">需协助内容</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr name="rowTrace" th:each="mapS,status:${exceptionTrackList}">
                        <td><a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeTraceRow(this)" title="删除选择行">x</a></td>
                        <td>
                            <input placeholder="请选择时间" type="text" class="form-control trackingdate"
                                   name="trackingDate" id="trackingDate0" th:value="${#dates.format(mapS.trackingDate, 'yyyy-MM-dd HH:mm:ss')}" readonly aria-required="false">
                        </td>
                        <td>
                            <input type="text" name="trackingContent" class="form-control" th:value="${mapS.trackingContent}"/>
                        </td>
                        <td>
                            <input type="text" name="assistanceContent" class="form-control" th:value="${mapS.assistanceContent}"/>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10 white-bg">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
</form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>

<script type="text/template" id="rowTrace">
    <tr name="rowTrace">
        <td><a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeTraceRow(this)" title="删除选择行">x</a></td>
        <td>
            <!--            <a class="close-link del-alink" onclick="removeIncomeRow(this)" title="删除行">-</a>-->
            <input placeholder="请选择时间" type="text" class="form-control trackingdate"
                   id="trackingDate" name="trackingDate" readonly required="false" aria-required="false">
        </td>
        <td>
            <input name="trackingContent" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off">
        </td>
        <td>
            <input name="assistanceContent" placeholder="" class="form-control valid" type="text" required="true">
        </td>
    </tr>
</script>

<script th:inline="javascript">
    var prefix = ctx + "trace";
    var entrustlot = ctx + "carrier/entrustLot";
    var moreprefix = ctx + "entrustexceptionmore";
    var carLen=[[${@dict.getType('car_len')}]];
    var payMethod = [[${@dict.getType('pay_method')}]];//付款方式
    var balaType=[[${@dict.getType('bala_type')}]];
    var exceptionType=[[${@dict.getType('exception_type')}]];
    var entrustExpAfter = [[${entrustExp}]];

    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related?permission=sales",1050,'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户名称
            $("#custName").val(rows[0]["custName"]);
            //客户简称
            $("#custAbbr").val(rows[0]["custAbbr"]);
            //选中完需单独校验
            $("#form-adnormal-add").validate().element($("#custAbbr"));


            let html="";
            let accidentTime=$("#accidentTime").val();
            let expTypes=$.form.selectCheckeds("expType");
            let custAbbr = $("#custAbbr").val()

            let expTypeName=[];
            expTypes.split(",").forEach(item=>{
                exceptionType.forEach(res=>{
                    if(item==res.dictCode){
                        expTypeName.push(res.dictLabel)
                    }
                })
            })

            html= html + accidentTime + " " + custAbbr + " " + expTypeName.join(',');
            $("#exceptionTitle").html(html);

            layer.close(index);
        });
    }

    function onExceptionTitle() {
        setTimeout(function () {
            let html="";
            let accidentTime=$("#accidentTime").val();
            let expTypes=$.form.selectCheckeds("expType");
            let custAbbr = $("#custAbbr").val()

            let expTypeName=[];
            expTypes.split(",").forEach(item=>{
                exceptionType.forEach(res=>{
                    if(item==res.dictCode){
                        expTypeName.push(res.dictLabel)
                    }
                })
            })

            html= html + accidentTime + " " + custAbbr + " " + expTypeName.join(',');
            $("#exceptionTitle").html(html);
        }, 1000)  
    }

    //新增校验逻辑
    function submitHandler() {
        if ($.validate.form()) {
            let html="";
            let accidentTime=$("#accidentTime").val();
            let expTypes=$.form.selectCheckeds("expType");
            let custAbbr = $("#custAbbr").val()

            let expTypeName=[];
            expTypes.split(",").forEach(item=>{
                exceptionType.forEach(res=>{
                    if(item==res.dictCode){
                        expTypeName.push(res.dictLabel)
                    }
                })
            })

            html= html + accidentTime + " " + custAbbr + " " + expTypeName.join(',');
            $("#exceptionTitle").html(html);

            $("#image").fileinput('upload');
            jQuery.subscribe("imgDone_1", commit);
        }
    }

    //新增提交
    function commit() {
        //验证是否存在在途费用
        //遍历选中的委托单
        var entrustIds = [];
        $("input[name='rmReq']").each(function () {
            if ($(this).is(":checked")) {
                entrustIds.push($(this).val());
            }
        });
        var ajaxParamsVO = {}; //参数类

        var entrustExp = new Object();
        //基础信息
        entrustExp.entrustExpId = entrustExpAfter.entrustExpId;
        entrustExp.isCustomerException = $("#isCustomerException").val();
        entrustExp.customerId = $("#customerId").val();
        entrustExp.exceptionTitle = $("#exceptionTitle").html();
        entrustExp.handleNote = $("#handleNote").val();
        entrustExp.caseStatus = $("#caseStatus").val();
        //是否锁定应付
        entrustExp.lockPay = ($('input:radio[name="lockPay"]:checked').val()!='' && $('input:radio[name="lockPay"]:checked').val()!=null)? $('input:radio[name="lockPay"]:checked').val() : 0;
        //是否报保险
        entrustExp.isInsurance = $('input:radio[name="isInsurance"]:checked').val();
        entrustExp.reportNo = $("#reportNo").val();
        //是否有残值
        entrustExp.abnormalLink = $('input:radio[name="abnormalLink"]:checked').val();
        //是否赔偿损失
        entrustExp.isCompensationRequired = $('input:radio[name="isCompensationRequired"]:checked').val();
        //异常类型
        var expTypes = $.form.selectCheckeds("expType");
        entrustExp.expType = expTypes;

        //出险信息
        entrustExp.accidentTime = $("#accidentTime").val();
        entrustExp.accidentReason = $("#accidentReason").val();
        entrustExp.preliminaryLoss = $("#preliminaryLoss").val();
        entrustExp.accidentPlace = $("#accidentPlace").val();
        //附件信息
        entrustExp.appendixId = $("#appendixId").val();
        //异常跟踪
        var exceptionTrackList = [];
        $("tr[name='rowTrace']").each(function () {
            var row = {};
            var trackingDate = $(this).find("input[name^='trackingDate']").val();
            var trackingContent = $(this).find("input[name^='trackingContent']").val();
            var assistanceContent = $(this).find("input[name='assistanceContent']").val();
            row.trackingDate = trackingDate;
            row.trackingContent = trackingContent;
            row.assistanceContent = assistanceContent;
            exceptionTrackList.push(row);
        });
        ajaxParamsVO.entrustExp = entrustExp //主异常
        ajaxParamsVO.exceptionTrackList = exceptionTrackList;//异常跟踪

        //询问框
        layer.confirm('确定保存异常？', {
            btn: ['确认','取消'] //按钮
        }, function(){
            $.operate.saveTabJson(moreprefix + "/updateEntrustException", ajaxParamsVO);
        }, function(){

        });
    }

    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
        //循环图片路径信息，初始化图片上传区域
        var files = [[${files}]];

        var image1 = {
            maxFileCount: 5,
            publish: "imgDone_1",  //用于绑定下一步方法
            fileType: null//文件类型
        };

        if(files == null){
            $.file.initAddFiles("image", "appendixId", image1);
        }else{
            $.file.loadEditFiles("image", "appendixId", files, image1);
        }
        //预估损失默认为0
        $("#lossAmount").val(0)

        //时间初始化
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#accidentTime', //指定元素
                type: 'date',
                trigger: 'click',
                // ready: function (date) {
                //     var now = new Date();
                //     this.dateTime.hours = now.getHours();
                //     this.dateTime.minutes = now.getMinutes();
                //     this.dateTime.seconds = now.getSeconds();
                // },
                done: function (value, date, endDate) {
                    $("#accidentTime").val(value);
                    //单独校验日期
                    $("#form-adnormal-add").validate().element($("#accidentTime"))
                }
            });

            laydate.render({
                elem: '#trackingDate0', //指定元素
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours = now.getHours();
                    this.dateTime.minutes = now.getMinutes();
                    this.dateTime.seconds = now.getSeconds();
                },
                done: function (value, date, endDate) {
                    $("#trackingDate0").val(value);
                    //单独校验日期
                    $("#form-adnormal-add").validate().element($("#trackingDate0"))
                }
            });
        });

        let isInsuranceNum=[[${entrustExp.isInsurance}]];
        if(isInsuranceNum==0){
            $("#reportNoDiv").css('display', 'none');
        }else{
            $("#reportNoDiv").css('display', 'block');
        }
        $('input[type=radio][name=isInsurance]').change(function() {
            let isInsurance=$('input:radio[name="isInsurance"]:checked').val();
            if(isInsurance==0){
                $("#reportNoDiv").css('display', 'none');
                $("#reportNo").val('');
            }else{
                $("#reportNoDiv").css('display', 'block');
            }
        });
    });

    var trackingIndex = 0;
    /** 添加跟踪记录 */
    function insertRowTrace() {
        //获取追踪时间的index
        trackingIndex++
        var rowTmpl = $("#rowTrace").html();
        rowTmpl = rowTmpl.replace("trackingDate", "trackingDate" + trackingIndex);
        rowTmpl = rowTmpl.replace("trackingContent", "trackingContent" + trackingIndex);
        rowTmpl = rowTmpl.replace("assistanceContent", "assistanceContent" + trackingIndex);

        $("#tracetab tbody").append(rowTmpl);
        layui.use('laydate', function(){
            var id = "#trackingDate" + trackingIndex;
            var laydate = layui.laydate;

            laydate.render({
                elem: '#trackingDate' + trackingIndex, //指定元素
                // format: 'yyyy-MM-dd',
                trigger: 'click',
                type: 'datetime',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                },
                done: function (value, date, endDate) {
                    $(id).val(value);
                    $("#form-adnormal-add").validate().element($(id));
                }
            });
        });
    }

    /* 删除追踪记录指定表格行 */
    function removeTraceRow(obj) {
        $("#tracetab tbody").find(obj).closest("tr").remove();
        trackingIndex--;
    }

</script>
</body>
</html>