<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收汇总列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: bootstrap-editable-css" />

</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 70px);
        padding-top: 0;
    }
    .flex{
        display: flex;
        align-items: center;
    }
    .tooltip-inner{
        max-width: 700px !important;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .label-dangerT{
        color: #ed5565;
        background-color: transparent;
        border: 1px solid #ed5565;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="salesDept" th:value="${salesDept}">
                <div class="row no-gutter">
                    <div class="col-md-12 col-sm-12">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" class="form-control" type="text"
                                       placeholder="请输入发货单号，单个发货单号为模糊搜索。多个单号搜索为精确搜索，每个单号之间用';'间隔" required="" aria-required="true">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-2 col-sm-4" style="padding: 0">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户订单号：</label>-->
                            <div class="col-sm-12">
                                <input name="custOrderno"  class="form-control"
                                       placeholder="请输入客户订单号" maxlength="50">
                            </div>
                        </div>
                    </div>
                 <!--   <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" class="form-control" type="text"
                                       placeholder="请输入发货单号" required="" aria-required="true">
                            </div>
                        </div>
                    </div>-->

                    <div class="col-md-2 col-sm-4" style="display: none">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">结算客户：</label>-->
                            <div class="col-sm-12">
                                <input name="balaName" id="balaName" placeholder="请输入结算客户"
                                       class="form-control valid" type="text" maxlength="25">
                                <input name="customerId" id="customerId" type="hidden" th:value="${customerId}">
                            </div>
                        </div>
                    </div>
                    <div style="padding: 0" class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">要求提货日：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]" placeholder="要求提货开始日">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]" placeholder="要求提货结束日">
                            </div>
                        </div>
                    </div>
                    <div style="padding: 0" class="col-md-1 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">应收单状态：</label>-->
                            <div class="col-sm-12">
                                <select name="receiveVbillstatus" id="receiveVbillstatus" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="应收单状态">
                                    <option></option>
                                    <option value="0">新建</option>
                                    <option value="1">已确认</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="goodsName"  class="form-control"  placeholder="货品" maxlength="50">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="arriContact"  class="form-control"  placeholder="客户收货人" maxlength="50">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">发货单状态：</label>-->
                            <div class="col-sm-12">
                                <select  id="invoice_vbillstatus" class="form-control selectpicker"
                                         aria-invalid="false" data-none-selected-text="发货单状态" multiple th:with="type=${invoiceStatusList}">
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="settlementCheck" id="settlementCheck" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="财务结算审核">
                                    <option></option>
                                    <option value="0">未审核</option>
                                    <option value="1">已审核</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" style="display: none">
                    <div class="col-md-4 col-sm-8">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">创建时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="params[startDate]" placeholder="创建开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="params[endtDate]" placeholder="创建结束时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">创建人：</label>-->
                            <div class="col-sm-12">
                                <input name="regUserName" id="regUserName" class="form-control"
                                       placeholder="请输入创建人" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">结算方式：</label>-->
                            <div class="col-sm-12">
                                <select name="balatype" id="balatype" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算方式" multiple
                                        th:with="type=${@dict.getType('bala_type')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>



                </div>
                <div class="row no-gutter">
                    <div class="col-sm-5">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">提货方地址：</label>-->
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select  name="params[deliProvinceId]" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[deliCityId]" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[deliAreaId]" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
<!--                            <div class="col-sm-3">-->
<!--                                <input name="params[deliDetailAddr]" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"-->
<!--                                       maxlength="50" autocomplete="off">-->
<!--                            </div>-->
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-10">
                                <div class="col-sm-4">
                                    <select  name="params[arriProvinceId]" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[arriCityId]" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[arriAreaId]" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
<!--                            <div class="col-sm-3">-->
<!--                                <input name="params[arriDetailAddr]" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"-->
<!--                                       maxlength="50" autocomplete="off">-->
<!--                            </div>-->
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
<!--            <a class="btn btn-primary" onclick="add()" th:if="${!isFleet}" shiro:hasPermission="finance:receive:insert">-->
<!--                <i class="fa fa-plus"></i> 新增-->
<!--            </a>-->
            <a class="btn btn-warning multiple disabled" onclick="affirm()" shiro:hasAnyPermissions="finance:receive:affirm,finance:fleet:receive:affirm">
                <i class="fa fa-mail-reply"></i> 确认
            </a>
<!--            <a class="btn btn-warning single disabled " th:if="${!isFleet}" onclick="reexamine()" shiro:hasPermission="finance:receive:reexamine">-->
<!--                <i class="fa fa-calculator"></i> 整单确认-->
<!--            </a>-->
            <a class="btn btn-danger single disabled" onclick="oppositeAffirm()" shiro:hasAnyPermissions="finance:receive:oppositeAffirm,finance:fleet:receive:oppositeAffirm">
                <i class="fa fa-exclamation"></i> 反确认
            </a>
            <a class="btn btn-primary multiple disabled " onclick="checking()" shiro:hasAnyPermissions="finance:receive:checking,finance:fleet:receive:checking">
                <i class="fa fa-file-text-o"></i> 生成对账单
            </a>
            <a class="btn btn-primary multiple disabled " onclick="insertChecking()" shiro:hasAnyPermissions="finance:receive:insertChecking,finance:fleet:receive:insertChecking">
                <i class="fa fa-file-text-o"></i> 加入对账单
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()"  shiro:hasAnyPermissions="finance:receive:export,finance:fleet:receive:export">
                <i class="fa fa-download"></i> 导出
            </a>
           <!-- <a class="btn btn-info"  onclick="$.table.importExcel()" th:if="${!isFleet}" shiro:hasAnyPermissions="finance:receive:adjustImport,finance:fleet:receive:adjustImport">
                <i class="fa fa-upload"></i> 调整单导入
            </a>
            <a class="btn btn-warning"  onclick="adjustExport()" th:if="${!isFleet}" shiro:hasAnyPermissions="finance:receive:adjustExport,finance:fleet:receive:adjustExport">
                <i class="fa fa-download"></i> 调整单导出
            </a>-->
            <!--<a class="btn btn-primary"  onclick="adjustRecord()" shiro:hasAnyPermissions="finance:receive:adjustRecord,finance:fleet:receive:adjustRecord">
                <i class="fa fa-newspaper-o"></i> 调整单记录
            </a>-->

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-table-editable-js" />
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a href="file/ReceiveAdjustModel.xlsx" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
<script id="scanCodeHtml" type="text/template">
    <div class="form-content">
        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-sm-2">
                        类型：
                    </label>
                    <div class="col-sm-10" id="feeTypeDiv">
                        <select id="feeType" onchange="changeFeeType()" class="form-control">
                            <option value="0" selected>运费</option>
<!--                            <option value="1">在途</option>-->
                        </select>
                    </div>
                   <!-- <div class="col-sm-5" id="costTypeOnWayDiv" style="display: none">
                        <select id="costTypeOnWay" class="form-control" th:with="type=${@dict.getType('cost_type_on_way')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>-->
                </div>
            </div>
        </div>
        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-sm-2">
                        金额：
                    </label>
                    <div class="col-sm-10">
                        <div class="input-group">
                        <span class="input-group-addon">￥</span>
                        <input type="text" class="form-control" name="freightTotal" id="freightTotal" oninput="$.numberUtil.onlyNumberTwoDecimal(this);">
                        <input type="text" style="display: none" class="form-control" name="onWayTotal" id="onWayTotal" oninput="$.numberUtil.onlyNumberTwoDecimal(this);">
                        </div>
                    </div>
                </div>
            </div> 
        </div>
        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-sm-2">
                        备注：
                    </label>
                    <div class="col-sm-10">
                        <textarea id="feeMemo" name="feeMemo" class="form-control" type="text" placeholder="备注"
                                  maxlength="250" required="" aria-required="true"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var vbillstatus = [[${invoiceStatusList}]]; //发货单状态
    //发货单状态map
    var invoiceStatusMap = [[${invoiceStatusMap}]];

    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];

    var prefix = isFleet ? ctx + "fleet/receive" : ctx + "receive";


    // 初始化省市区
    $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
    $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

    //客户id
    var customerId = $("#customerId").val();

    //合计
    var receiptAmountFreightTotal = 0;//运费
    var receiptAmountOnWayTotal = 0;//在途
    var receiptAmountTotal = 0;//总金额
    var collectAmountTotal = 0;//总金额
    var acReceiptAmountTotal = 0;//实际总金额
    var gotAmountTotal = 0;//已收
    var ungotAmountTotal = 0;//未收
    var costCountTotal = 0;//总成本
    var costCountFreightTotal = 0;//运费总成本
    var costCountOnWayTotal = 0;//在途总成本
    var otherFeeCountTotal = 0;//第三方应付
    var managerFeeCountTotal = 0;//管理费
    var grossProfitTotal = 0;//毛利
    $(function () {


        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add?customerId="+$("#customerId").val(),
            exportUrl: prefix + "/export",
            importUrl: ctx + "receive/adjustImport",
            showToggle: false,
            showColumns: true,
            modalName: "应收明细",
            fixedColumns: false,
            rememberSelected: false,
            // fixedNumber: 4,
            clickToSelect: true,
            showFooter:true,
            height: 560,
            uniqueId: "uniqueId",
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1,7],
                exportHiddenColumns: [8,9],
                fileName:"客户应收明细"
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onEditableSave: function (field, row, oldValue, $el) {
                var isClose = row.isClose;
                if (field === 'custOrderno') {
                    data = {invoiceId: row.invoiceId,orderNo:row.custOrderno}
                    editCustOrderno(data);
                    return;
                }
                if(isClose == 1){
                    $.modal.alertWarning("该月份已关账，无法进行操作！");
                    $el.text(oldValue)
                    return false;
                }if (row.isFleetData == '1' && row.isFleetAssign == '1'){
                    $.modal.alertWarning("分配而来的单子无法修改！");
                    return false;
                }else{
                    var data;
                    if (field === 'freightTotal') {
                        data = {invoiceId: row.invoiceId, amount: row.freightTotal,status: row.receiveVbillstatus, type: 0};
                        editReceive(data);
                    } else if(field === 'onWayTotal'){
                        data = {invoiceId: row.invoiceId, amount: row.onWayTotal,status: row.receiveVbillstatus, type: 1};
                        editReceive(data);
                    }
                }

            },
            onPostBody: function () {
                getAmountCount();
            },
            columns: [{
                checkbox: true,
                forceExport :true,
                footerFormatter: function (row) {
                    return "合计:&nbsp&nbsp"
                        + "运费:<nobr id='receiptAmountFreightTotal'>￥0</nobr>&nbsp&nbsp"
                        + "在途:<nobr id='receiptAmountOnWayTotal'>￥0</nobr>&nbsp&nbsp"
                        + "总金额:<nobr id='receiptAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "（到付：<nobr id='collectAmountTotal'>￥0</nobr>&nbsp&nbsp实际总金额：<nobr id='acReceiptAmountTotal'>￥0</nobr>）<br>"
                        // + "总成本:<nobr id='costCountTotal'>￥0</nobr>&nbsp&nbsp"
                        // + "运费总成本:<nobr id='costCountFreightTotal'>￥0</nobr>&nbsp&nbsp"
                        // + "在途总成本:<nobr id='costCountOnWayTotal'>￥0</nobr>&nbsp&nbsp"
                        // + "第三方应付:<nobr id='otherFeeCountTotal'>￥0</nobr>&nbsp&nbsp"
                        // /* + "管理费:<nobr id='managerFeeCountTotal'>￥0</nobr>&nbsp&nbsp"*/
                        // + "毛利:<nobr id='grossProfitTotal'>￥0</nobr>&nbsp&nbsp<br>" +
                        + "<b>总合计：</b>  运费类型金额：<nobr id='allFreightTotal'>￥0</nobr>&nbsp&nbsp" +
                        "在途类型金额：<nobr id='allOnWayTotal'>￥0</nobr>&nbsp&nbsp" +
                        "总金额：<nobr id='allTransFeeCount'>￥0</nobr>" +
                        "（到付：<nobr id='collectAmountCount'>￥0</nobr>&nbsp&nbsp实际总金额：<nobr id='acAllTransFeeCount'>￥0</nobr>）"
                        // "总成本：<nobr id='allCostCount'>￥0</nobr>&nbsp&nbsp" +
                        // "运费总成本：<nobr id='allCostCountFreight'>￥0</nobr>&nbsp&nbsp" +
                        // "在途总成本：<nobr id='allCostCountOnWay'>￥0</nobr>&nbsp&nbsp" +
                        // "第三方应付：<nobr id='allOtherFeeCount'>￥0</nobr>&nbsp&nbsp<br>" +
                        // /*"管理费：<nobr id='allManagerFeeCount'>￥0</nobr>&nbsp&nbsp<br>"+*/
                        // "<b>毛利：<nobr id='noAllGrossProfit'>￥0</nobr>&nbsp&nbsp";

                }
            }, {
                title: '操作',
                align: 'left',
                field: 'invoiceId',
                formatter: function (value,row,index) {
                    var actions = [];
                    if ([[${@permission.hasPermi('finance:receive:receiveDetail')}]] != "hidden" ||
                        [[${@permission.hasPermi('finance:fleet:receive:receiveDetail')}]] != "hidden") {
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + row.invoiceId + '\',\''+row.isFleetData +'\',\'' + row.isFleetAssign +'\',\''+row.receiveVbillstatus+'\')"><i  class="fa fa-cny" style="font-size: 15px;" ></i></a>');
                    }
                  
                        var now = new Date();
                        let reqDeliDate=new Date(Date.parse(row.reqDeliDate))
                        reqDeliDate=reqDeliDate.setDate(reqDeliDate.getDate()+5)
                        reqDeliDate=new Date(reqDeliDate)

                    if ([[${@permission.hasAnyPermi('finance:receive:adjust,finance:fleet:receive:adjust')}]] != "hidden"){
                        let data=JSON.stringify({
                            isClose : row.isClose,
                            isFleetData : row.isFleetData,
                            isFleetAssign : row.isFleetAssign,
                            invoiceId: row.invoiceId,
                            receiveVbillstatus: row.receiveVbillstatus,
                            freightTotal: row.freightTotal,
                            onWayTotal: row.onWayTotal,
                            settlementCheck:row.settlementCheck,
                            receiptStatus:row.receiptStatus
                        }).replace(/"/g, '&quot;');

                        if (row.isClose != 1 && row.settlementCheck == 0 && row.receiptStatus != 0 && row.receiptStatus != 1) {

                        } else {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="应收调整" onclick="adjust(' + data + ')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }

                    }

                    return actions.join('');
                }

            },
                {title: '客户单号/简称',
                    align: 'left',
                    field: 'custOrderno',
                    formatter: function status(value, row, index) {
                        let html= [];
                        if(value){
                            html.push(value);
                        }else{
                            html.push("-");
                        }
                        if (row.bizCustAbbr != null) {
                            html.push( row.custAbbr + '-' + row.bizCustAbbr);
                        }else {
                            html.push( row.custAbbr);
                        }
                       return html.join("<br/>");
                    }
                },
                {title: '发货单号/状态',
                    align: 'left',
                    field: 'vbillno',
                    // sortable:true,
                    formatter: function status(row,value) {
                        var context = '';
                        vbillstatus.forEach(function (v) {
                            if (v.value == value.invoiceStatus) {

                                if (value.invoiceStatus == invoiceStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_PICK_UP
                                    || value.invoiceStatus == invoiceStatusMap.PICK_UP) {
                                    //部分提货 与 已提货
                                    context = '<span class="label label-info">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_ARRIVALS
                                    || value.invoiceStatus == invoiceStatusMap.ARRIVALS) {
                                    //部分到货  已到货
                                    context = '<span class="label label-success">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_RETURNS
                                    || value.invoiceStatus == invoiceStatusMap.RETURNS) {
                                    //部分回单  已回单
                                    context = '<span class="label label-primary">' + v.context + '</span>';
                                } else {
                                    //关闭
                                    context = '<span class="label label-inverse">' + v.context + '</span>';
                                }

                                return false;
                            }
                        });

                        let settlementCheck="";
                        if (value.custSettlementCheck == 0) {
                            settlementCheck= `<div class='ml5'><i class='fa fa-check text-primary f16' data-toggle='tooltip' data-placement='top' data-html='true' title="该客户无需审核"></i></div>`;
                        }else {
                            if(value.settlementCheck==1){
                                settlementCheck= `<div class='ml5'><i class='fa fa-check text-primary f16' data-toggle='tooltip' data-placement='top' data-html='true' title="`+getDailList(value.invoiceId)+`"></i></div>`;
                            }else{
                                settlementCheck= "<div class='ml5'><i class='fa fa-hourglass-end text-danger f16' data-toggle='tooltip' data-placement='top' data-html='true' title='待结算组审核'></i></div>";
                            }
                        }

                        return "<div class='flex'><div>"+row+"<br/>"+context+"</div>"+settlementCheck+"</div>"
                    }
                },
                
                {title: '提货日',
                    align: 'left', 
                    field: 'reqDeliDate',
                    formatter: function status(value, row, index) {
                        let html = [];
                        // 第一行：要求日期
                        let reqDate = (row && row.reqDeliDate) ? String(row.reqDeliDate).substring(0, 10) : "-";
                        html.push('<span class="label label-warning pa2">要求</span> ' + reqDate);

                        // 第二行：实际日期
                        let actDate = (row && row.actDeliDate) ? row.actDeliDate : "-";
                        html.push('<span class="label label-info pa2">实际</span> ' + actDate);

                        return html.join("<br/>");
                    }
                },
                // {title: '提货省市区', align: 'left', field: 'deliAddr',visible: false},
                // {title: '到货省市区', align: 'left', field: 'arriAddr',visible: false},
                {title: '提货|到货省市区', align: 'left', field: 'deliAddr',
                    formatter: function status(value, row, index) {
                        let html = '';
                        let multipleShippingAddressList = row.multipleShippingAddressList;
                        console.log(multipleShippingAddressList)
                        for (const elem of multipleShippingAddressList) {
                            if (elem.addressType == 0) {
                                let addr = elem.provinceName  + elem.cityName + elem.areaName;
                                html += `<span class="label label-warning pa2">提</span>${addr}<br/>`
                            }
                        }
                        for (const elem of multipleShippingAddressList) {
                            if (elem.addressType == 1) {
                                let addr = '';
                                let caAddr = '';
                                if(elem.isChangeAddress == 1){
                                    addr = elem.caArriProName + elem.caArriCityName + elem.caArriAreaName;
                                    caAddr = elem.provinceName  + elem.cityName + elem.areaName;
                                    html += `<span class="label label-danger pa2"
                                  style="" data-toggle="tooltip"
                                  data-placement="left" data-html="true" title="${caAddr}">改</span>`;
                                    html += `<span class="label label-success pa2">到</span>${addr}<br/>`
                                }else{
                                    addr = elem.provinceName  + elem.cityName + elem.areaName;
                                    html += `<span class="label label-success pa2">到</span>${addr}<br/>`
                                }


                            }
                        }
                    /*    <span className="label label-success pa2">到</span>
                        html = `<span class="label label-warning pa2">提</span>${row.deliAddr}<br/><span class="label label-success pa2">到</span>`

                        if (row.isChangeAddress == 1) {
                            html = html + `<span class="label label-dangerT"
                              style="padding:1px;cursor: pointer;" data-toggle="tooltip"
                              data-placement="left" data-html="true" title="${row.arriAddr}">改</span>${row.caArriAddr}`;
                        }else {
                            html = html + row.arriAddr
                        }*/

                        return html;
                    }
                },
                {title: '货品信息',
                    align: 'left',
                    field : 'goodsName',
                    formatter: function status(value, row, index) {
                       let html = [];
                       if(row.numCount){
                            html.push(row.numCount+"件") 
                        }
                        if(row.weightCount){
                            html.push(row.weightCount+"吨") 
                        }
                        if(row.volumeCount){
                            html.push(row.volumeCount+"m³") 
                        }

                       return value+"<br/>"+html.join("/");
                    }
                },
                {title: '车长车型',
                    align: 'left',
                    field: 'carLenAndType',
                    formatter: function status(value, row, index) {
                        let html = '';
                        if (value) {
                            html = value;
                        }
                        if (row.carNo) {
                            html = html + '<br/>' + $.table.tooltip(row.carNo);
                        }


                        return html;
                    }
                },
                {title: '应收/单据状态',
                    align: 'left',
                    field : 'freightTotal',
                    formatter: function status(value, row, index) {
                        let html=[];
                        if(row.freightTotal){

                            let data=JSON.stringify({
                                isClose : row.isClose,
                                isFleetData : row.isFleetData,
                                isFleetAssign : row.isFleetData,
                                invoiceId: row.invoiceId,
                                receiveVbillstatus: row.receiveVbillstatus,
                                freightTotal: row.freightTotal
                            }).replace(/"/g, '&quot;');

                            html.push("<span class='text-navy mr10'>运费</span>"+row.freightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.onWayTotal){
                            html.push("<span class='text-navy mr10'>在途</span>"+row.onWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if (row.collectAmount) {
                            html.push("<span class='text-warning mr10'>到付</span>"+row.collectAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }

                        //单据状态
                        let statusLabel = "";
                        if (row && typeof row.receiveVbillstatus !== "undefined" && row.receiveVbillstatus !== null) {
                            switch (row.receiveVbillstatus) {
                                case 0:
                                    statusLabel = '<span class="label label-primary">新建</span>';
                                    break;
                                case 1:
                                    statusLabel = '<span class="label label-warning">已确认</span>';
                                    break;
                                case 2:
                                    statusLabel = '<span class="label label-coral">已对账</span>';
                                    break;
                                case 3:
                                    statusLabel = '<span class="label label-info">部分核销</span>';
                                    break;
                                case 4:
                                    statusLabel = '<span class="label label-success">已核销</span>';
                                    break;
                                case 5:
                                    statusLabel = '<span class="label label-inverse">关闭</span>';
                                    break;
                                default:
                                    statusLabel = "";
                                    break;
                            }
                        } else {
                            statusLabel = "-";
                        }
                        html.push(statusLabel);

                        return html.join("<br/>");
                    }
                },
                {title: '回单状态',
                    align: 'left',
                    field : 'receiptStatus',
                    formatter: function status(value, row, index) {
                        let html=[]
                        switch (value) {
                            case 0: 
                                html.push( '<span class="carve carve-success">已确认</span>' );
                                break;
                            case 1: 
                                html.push( '<span class="carve carve-coral">部分确认</span>' );
                                break;
                            case 2: 
                                html.push( '<span class="carve carve-primary">已上传</span>' );
                                break;
                            case 3: 
                                html.push( '<span class="carve carve-warning">部分上传</span>' );
                                break;
                            case 4: 
                                html.push( '<span class="carve carve-danger">未上传</span>' );
                                break;
                            default: 
                                break;
                        }

                       return html.join("<br/>")
                    }
                },  
                

                // {title: '应付',
                //     align: 'left',
                //     field : 'costCountFreight',
                //     formatter: function status(value, row, index) {
                //         let html=[];
                //         let isAdjust="";
                //         // if(row.isAdjust == 1){
                //         //     isAdjust="<span class='label label-success pa2 ml5'>调</span>"
                //         // }
                //         if(row.costCountFreight){
                //             html.push("<span class='text-navy mr10'>运费</span>"+row.costCountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+isAdjust)
                //         }
                //         if(row.costCountOnWay){
                //             html.push("<span class='text-warning mr10'>在途</span>"+row.costCountOnWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                //         }
                //         return html.join("<br/>")
                //     }
                // },

                // {title: '运费(元)', align: 'left', field: 'freightTotal',
                //     editable:{
                //         type: 'text',
                //         disabled: false,
                //         title: '修改运费应收',   
                //         validate:  function (v) {
                //             if (!checkNumber(v)) {
                //                 return "请输入数字！";
                //             }
                //         }
                //     }
                // },
                // {title: '在途(元)', align: 'left', field: 'onWayTotal',
                    // editable:{
                    //     type: 'text',
                    //     title: '修改在途应收',
                    //     validate:  function (v) {
                    //         if (!checkNumber(v)) {
                    //             return "请输入数字！";
                    //         }
                    //     }
                    // }
                // },
                // {title: '总金额(元)', align: 'left', field: 'transFeeCount'},
                // {title: '调整后金额(元)', align: 'left', field: 'adjustAmount'},
                // {title: '已收金额', align: 'left', field: 'gotAmount'},
                // {title: '未收金额', align: 'left', field: 'ungotAmount'},
                // {title: '总成本(元)', align: 'right', field: 'costCount'},
                // {title: '运费总成本(元)', align: 'right', field: 'costCountFreight'},
                // {title: '在途总成本(元)', align: 'right', field: 'costCountOnWay'},
                // {title: '第三方应付(元)', align: 'right', field: 'otherFeeCount',
                //     formatter: function status(value, row, index) {
                //         let html=[];
                //         if(value){
                //             html.push(value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                //         }
                //         return html.join()
                //     }
                // },
                /*                {title: '管理费(元)', align: 'right', field: 'managerFee'},*/
                // {title: '毛利(元)', align: 'right', field: 'grossProfit',
                //     formatter: function status(value, row, index) {
                //         let html=[];
                //         if(value){
                //             html.push(value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                //         }
                //         return html.join()
                //     }
                // },
                
                {title: '回单照片',
                    align: 'left',
                    field : 'receiptUploadFiles',
                    formatter: function status(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img style="height:32px" src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                {title: '结算客户/结算方式', align: 'left', field: 'balaName',
                    formatter : function status(value, row, index) {
                        return value+"<br/>"+$.table.selectDictLabel(balatype, row.balaType)
                    }
                },
                {title: '运营公司',align: 'left', field: 'balaCorpId',
                    formatter : function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value);
                    }
                }
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;

        });
        layui.use('laydate', function() {
            var reqDeliDateStart = layui.laydate;

        });
        layui.use('laydate', function() {
            var reqDeliDateEnd = layui.laydate;

        });
    });
    //获取字符长度
    function getStringLen(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
            str.charCodeAt(i) > 255 ? len += 2 : len += 1;
        }
        return len;
    }

    /**
     * 修改运费或在途费用
     */
    function editReceive(data) {
        $.ajax({
            url: ctx + "receive/edit_receive",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code === 0) {
                    $.modal.msgSuccess("调整成功");
                    var data = result.data;
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                } else {
                    $.modal.msgError(result.msg);
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            }
        });
    }

    /**
     * 修改客户发货单号
     */
    function editCustOrderno(data) {
        $.ajax({
            url: ctx + "receivableReconciliation/edit_order_no",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code === 0) {
                    var data = result.data;
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                } else {
                    $.modal.msgError(result.msg);
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        receiptAmountFreightTotal = 0;//运费
        receiptAmountOnWayTotal = 0;//在途
        receiptAmountTotal = 0;//总金额
        collectAmountTotal = 0;//代收
        acReceiptAmountTotal = 0;//实际总金额
        // costCountTotal = 0;//总成本
        // costCountFreightTotal = 0;//运费总成本
        // costCountOnWayTotal = 0;//在途总成本
        // otherFeeCountTotal = 0;//第三方应付
        // managerFeeCountTotal = 0;//管理费
        // grossProfitTotal = 0;//毛利
    }
    /**
     * 累计总金额
     */
    function addTotal(row) {
        receiptAmountFreightTotal = receiptAmountFreightTotal + row.freightTotal + row.onWayTotal;//运费
        receiptAmountOnWayTotal = receiptAmountOnWayTotal + row.onWayTotal;//总金额
        receiptAmountTotal = receiptAmountTotal + row.transFeeCount;//总金额

        let collectAmount = row.collectAmount
        collectAmountTotal = collectAmountTotal + collectAmount;//代收
        acReceiptAmountTotal = receiptAmountFreightTotal - collectAmountTotal
        // costCountTotal = costCountTotal + row.costCount;//总成本
        // costCountFreightTotal = costCountFreightTotal + row.costCountFreight;//运费总成本
        // costCountOnWayTotal = costCountOnWayTotal + row.costCountOnWay;//在途总成本
        // otherFeeCountTotal = otherFeeCountTotal + row.otherFeeCount;//第三方应付
        // managerFeeCountTotal = managerFeeCountTotal + row.managerFee;//管理费
        // grossProfitTotal = grossProfitTotal + row.grossProfit;//毛利
    }

    function subTotal(row) {
        receiptAmountFreightTotal = receiptAmountFreightTotal - row.freightTotal - row.onWayTotal;//运费
        receiptAmountOnWayTotal = receiptAmountOnWayTotal - row.onWayTotal;//总金额
        receiptAmountTotal = receiptAmountTotal - row.transFeeCount;//总金额

        let collectAmount = row.collectAmount
        collectAmountTotal = collectAmountTotal - collectAmount;//代收
        acReceiptAmountTotal = receiptAmountFreightTotal - collectAmountTotal

        // costCountTotal = costCountTotal - row.costCount;//总成本
        // costCountFreightTotal = costCountFreightTotal - row.costCountFreight;//运费总成本
        // costCountOnWayTotal = costCountOnWayTotal - row.costCountOnWay;//在途总成本
        // otherFeeCountTotal = otherFeeCountTotal - row.otherFeeCount;//第三方应付
        // managerFeeCountTotal = managerFeeCountTotal - row.managerFee;//管理费
        // grossProfitTotal = grossProfitTotal - row.grossProfit;//毛利
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#receiptAmountFreightTotal").text(receiptAmountFreightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountOnWayTotal").text(receiptAmountOnWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountTotal").text(receiptAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#collectAmountTotal").text(collectAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#acReceiptAmountTotal").text(acReceiptAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#costCountTotal").text(costCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#costCountFreightTotal").text(costCountFreightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#costCountOnWayTotal").text(costCountOnWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#otherFeeCountTotal").text(otherFeeCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#managerFeeCountTotal").text(managerFeeCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#grossProfitTotal").text(grossProfitTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.balaType = $.common.join($('#balatype').selectpicker('val'));
        data.params = new Map();
        data.invoiceStatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));

        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    //总金额
                    $("#allTransFeeCount").text(data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //运费类型金额
                    $("#allFreightTotal").text(data.freightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //在途类型金额
                    $("#allOnWayTotal").text(data.onWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                    let collectAmountCount = data.collectAmountCount;

                    let acAllTransFeeCount = parseFloat(data.transFeeCount) - parseFloat(collectAmountCount);
                    $("#collectAmountCount").text(collectAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#acAllTransFeeCount").text(acAllTransFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                    //
                    // //总成本
                    // $("#allCostCount").text(data.costCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // //运费总成本
                    // $("#allCostCountFreight").text(data.costCountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // //在途总成本
                    // $("#allCostCountOnWay").text(data.costCountOnWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // //第三方应付
                    // $("#allOtherFeeCount").text(data.otherFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // //管理费
                    // $("#allManagerFeeCount").text(data.managerFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // //毛利
                    // $("#allGrossProfit").text(data.grossProfit.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // //不含管理费毛利
                    // $("#noAllGrossProfit").text(data.noGrossProfit.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 生成对账单的方法
     */
    function checking() {
        var rows =  $.table.selectColumns("invoiceId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var hasApplyLen = 0;
        //结算客户id
        var balaCustomer = bootstrapTable[0]["balaCustomer"];
        var salesDept = bootstrapTable[0]["salesDept"];
        var vbillno = bootstrapTable[0]["vbillno"];

        var hasCollectAmount = 0
        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["receiveVbillstatus"] !== 1) {
                $.modal.alertWarning("生成对账单的应收单据只能为已确认状态");
                return;
            }
            if (bootstrapTable[i]["balaCustomer"] !== balaCustomer) {
                $.modal.alertWarning("所选结算客户不相同");
                return;
            }
            /*if (bootstrapTable[i]["salesDept"] !== salesDept) {
                $.modal.alertWarning(bootstrapTable[i]["vbillno"]+"与"+vbillno+"属于不同的运营组，请分开打包");
                return;
            }*/

            //判断是否单独申请
            if (bootstrapTable[i]["hasApply"] === 1) {
                hasApplyLen = hasApplyLen + 1;
            }

            if (bootstrapTable[i]["collectAmount"] !=null && bootstrapTable[i]["collectAmount"] != 0) {
                hasCollectAmount = 1
            }

        }
        //当都为单独申请时 无法生成
        if (hasApplyLen === bootstrapTable.length) {
            $.modal.alertWarning("没有符合的应收数据，无法生成对账单！");
            return;
        }

        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        //判断get请求长度
        var mb = myBrowser();
        if ("IE" == mb) {
            if(rows.join().length>2000){
                $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        if ("Chrome" == mb) {
            if(rows.join().length>8000){
                $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }


        if (hasCollectAmount == 1) {
            var lock = false;
            layer.confirm("存在司机代收，生成对账单时，司机代收的应收数据将自动被过滤。", {
                // area:area,
                icon: 0,
                title: "是否确定生成对账单",
                btn: ['确定', '取消']
            }, function (index) {
                if (!lock) {
                    lock = true
                    layer.close(index);
                    $.modal.openTab("生成对账单", ctx + "receive/checking?invoiceId=" + rows.join() + "&receiveDetsailIds=");
                }
            });
        }else {
            $.modal.openTab("生成对账单", ctx + "receive/checking?invoiceId=" + rows.join() + "&receiveDetsailIds=");
        }

    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["receiveVbillstatus"] !== 1) {
            $.modal.alertWarning("应收单据只能为已确认状态下才能反确认");
            return;
        }
        if (bootstrapTable[0]["hasApply"] === 1) {
            $.modal.alertWarning("存在单独申请的应收，无法反确认！");
            return;
        }
        if (bootstrapTable[0]["isClose"] === 1 ) {
            $.modal.alertWarning("请选择未在关账日期内的应收单据");
            return;
        }

        var invoiceId =  $.table.selectColumns("invoiceId");
        if (invoiceId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("反确认", ctx + "receive/back_confirm/" + invoiceId,500,300);
    }

    /**
     * 确认应收明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["receiveVbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能确认");
                return;
            }
            // if (bootstrapTable[i]["isClose"] === 1 ) {
            //     $.modal.alertWarning("请选择未在关账日期内的应收单据");
            //     return;
            // }
        }

        var invoiceId =  $.table.selectColumns("invoiceId");
        if (invoiceId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.confirm("是否确认？", function () {
            $.operate.post(ctx + "receive/affirm", {"invoiceIds": invoiceId.join()});
        });
    }

    //新增
    function add() {
        var rows =  $.table.selectColumns("invoiceId");
        if(rows.length == 0 ){
            var url  = ctx + "receive/add?customerId="+$("#customerId").val();
            $.modal.openTab("添加应收明细", url);
        }else if(rows.length ==1){
            // 选中的行
            var bootstrapTable = $.btTable.bootstrapTable('getSelections');
            if (bootstrapTable[0]["receiveVbillstatus"] !== 0) {
                $.modal.alertWarning("请选择新建状态的数据！");
                return;
            }

            var url  = ctx + "receive/add?customerId="+$("#customerId").val()+"&invoiceId="+rows.join();
            $.modal.openTab("添加应收明细", url);
        }else {
            $.modal.alertWarning("最多选择一条应收记录");
        }
    }


    /**
     * 加入对账单的方法
     */
    function insertChecking() {
        var rows =  $.table.selectColumns("invoiceId");

        var hasApplyLen = 0;
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var salesDept = bootstrapTable[0]["salesDept"];
        var vbillno = bootstrapTable[0]["vbillno"];
        var hasCollectAmount = 0

        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["receiveVbillstatus"] !== 1) {
                $.modal.alertWarning("加入对账单的应收单据只能为已确认");
                return;
            }
            /*if (bootstrapTable[i]["salesDept"] !== salesDept) {
                $.modal.alertWarning(bootstrapTable[i]["vbillno"]+"与"+vbillno+"属于不同的运营组，请分开打包");
                return;
            }*/
            //判断是否单独申请
            if (bootstrapTable[i]["hasApply"] === 1) {
                hasApplyLen = hasApplyLen + 1;
            }

            if (bootstrapTable[i]["collectAmount"] !=null && bootstrapTable[i]["collectAmount"] != 0) {
                hasCollectAmount = 1
            }

        }

        //当都为单独申请时 无法生成
        if (hasApplyLen === bootstrapTable.length) {
            $.modal.alertWarning("没有符合的应收数据，无法生成对账单！");
            return;
        }

        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var fleet = isFleet ? 1 : 0;

        var customerId =  bootstrapTable[0]["customerId"];
        var getStr = "/insertChecking?customerId=" + customerId + "&invoiceIds=" + rows + "&receiveDetsailIds=" + "&salesDept=" + salesDept + "&isFleet=" + fleet;

        //判断get请求长度
        var mb = myBrowser();
        if ("IE" == mb) {
            if(getStr>2000){
                $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        if ("Chrome" == mb) {
            if(getStr>8000){
                $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }

        if (hasCollectAmount == 1) {
            var lock = false;
            layer.confirm("存在司机代收，加入对账单时，司机代收的应收数据将自动被过滤。", {
                // area:area,
                icon: 0,
                title: "是否确定加入对账单",
                btn: ['确定', '取消']
            }, function (index) {
                if (!lock) {
                    lock = true
                    layer.close(index);
                    $.modal.open("加入对账单", ctx + "receive" + getStr);
                }
            });

        }else {
            $.modal.open("加入对账单", ctx + "receive" + getStr);
        }

        // var url = ctx + "receive" + getStr;
        // parent.layer.open({
        //     type: 2,
        //     maxmin: true,
        //     shade: false,
        //     title: "加入对账单",
        //     area: ['85%','100%'],
        //     content: url,
        //     shadeClose: true,
        //     btn: ['<i class="fa fa-close"></i> 关闭'],
        //     yes: function (index, layero) {
        //         parent.layer.close(index);
        //     }
        // });
    }

    /**
     * 分批收款
     */
    function batchRece(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        if (bootstrapTable[0]["vbillstatus"] !== 1 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已确认/部分核销的应收单");
            return;
        }
        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"]) {
            $.modal.alertWarning("该应收单已完成");
            return;
        }
        var url = prefix + "/batchRece?receiveDetailId="+bootstrapTable[0]["receiveDetailId"];
        $.modal.open('分批收款',url);
    }


    /**
     * 详情
     */
    function detail(invoiceId, isFleet, isFleetAssign, receiveVbillstatus) {
        var isf = isFleet === "1" ? true : false
        var isfa = isFleetAssign === "1" ? true : false

        var url = ctx + "receive/receive_detail?invoiceId=" + invoiceId + "&isFleet=" + isf + "&isFleetAssign=" + isfa + "&vbillstatus=" + receiveVbillstatus;
        $.modal.openTab('应收详情', url);
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.balaType = $.common.join($('#balatype').selectpicker('val'));
        data.params = new Map();
        data.invoiceStatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 复核
     */
    function reexamine() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        if (bootstrapTable[0]["receiveVbillstatus"] !== 0) {
            $.modal.alertWarning("应收单据只能为新建状态下才能复核确认");
            return;
        }
        if (bootstrapTable[0]["isClose"] === 1 ) {
            $.modal.alertWarning("请选择未在关账日期内的应收单据");
            return;
        }

        var invoiceId =  $.table.selectColumns("invoiceId");
        if (invoiceId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = ctx + "receive/reexamine?invoiceId="+invoiceId;
        $.modal.openTab("复核", url);
    }

    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        var deliDetailAddr= $('#deliDetailAddr').val()
        var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        $('#deliDetailAddr').val(arriDetailAddr)
        $('#arriDetailAddr').val(deliDetailAddr)
        searchPre();
    }

    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function myBrowser(){
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1){
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            return "IE";
        }; //判断是否IE浏览器
    }

    function adjustExport(){
        var data = $("#role-form").serializeArray();
        var invoiceStatusList = {};
        invoiceStatusList.name = "invoiceStatus"
        invoiceStatusList.value = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        data.push(invoiceStatusList);
        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "receive/exportAdjust", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function adjustRecord(){
        var url = ctx + "receive/adjustRecordCheck";
        $.modal.openTab('调整单记录',url);
    }
    function adjust(row) {
        // isClose : row.isClose,
        //     isFleetData : row.isFleetData,
        //     isFleetAssign : row.isFleetData,
        //     invoiceId: row.invoiceId,
        //     receiveVbillstatus: row.receiveVbillstatus,
        //     freightTotal: row.freightTotal,
        //     settlementCheck:row.settlementCheck,
        //     receiptStatus:row.receiptStatus

  /*      if (row.isClose != 1 && row.settlementCheck == 0 && row.receiptStatus != 0 && row.receiptStatus != 1) {
            if (row.isFleetData == '1' && row.isFleetAssign == '1'){
                $.modal.alertWarning("分配而来的单子无法修改！");
                return false;
            }else{
                layer.open({
                    type: 1,
                    area: ['40%', '30%'],
                    fix: false,
                    maxmin: true,
                    shade: 0.3,
                    title: "修改运费",
                    content: $("#scanCodeHtml").html(),
                    btn: ['保存', '关闭'],
                    shadeClose: true,
                    success: function (layero, index) {
                        $("#freightTotal").val(row.freightTotal)
                        $("#onWayTotal").val(row.onWayTotal)
                    },
                    yes: function (index, layero){
                        var data = {}

                        let feeType = $("#feeType").val();
                        if (feeType == 0) {
                            data = {
                                invoiceId: row.invoiceId,
                                amount: $("#freightTotal").val(),
                                memo: $("#feeMemo").val(),
                                status: row.receiveVbillstatus,
                                type: 0
                            };
                        }else if (feeType == 1) {
                            data = {
                                invoiceId: row.invoiceId,
                                amount: $("#onWayTotal").val(),
                                memo: $("#feeMemo").val(),
                                costTypeOnWay: $("#costTypeOnWay").val(),
                                status: row.receiveVbillstatus,
                                type: 1
                            };
                        }
                        editReceive(data);

                        layer.close(index);
                    },
                    cancel: function(index) {
                        return true;
                    }
                })
            }
        }else {
            var url = ctx + "trace/addPayDetailAdjust?invoiceLotId="+row.invoiceId+"&adjustType=0";
            $.modal.open("调账", url);
        }*/

        var url = ctx + "trace/addPayDetailAdjust?invoiceLotId="+row.invoiceId+"&adjustType=0";
        $.modal.open("调账", url);

    }

    function changeFeeType() {
        if ($('#feeType').val() === "0") { // 选中“运费”选项
            $("#feeTypeDiv").removeClass("col-sm-5").addClass("col-sm-10");
            $('#costTypeOnWayDiv').hide();

            $('#onWayTotal').hide();
            $('#freightTotal').show();
        } else if ($('#feeType').val() === "1") { // 选中“在途”选项
            $("#feeTypeDiv").removeClass("col-sm-10").addClass("col-sm-5");
            $('#costTypeOnWayDiv').show();

            $('#freightTotal').hide();
            $('#onWayTotal').show();
        }

    }

    // function onfreightTotal(row) {
    //     var isClose = row.isClose;
    //
    //     if(isClose == 1){
    //         $.modal.alertWarning("该月份已关账，无法进行操作！");
    //         $el.text(oldValue)
    //         return false;
    //     }if (row.isFleetData == '1' && row.isFleetAssign == '1'){
    //         $.modal.alertWarning("分配而来的单子无法修改！");
    //         return false;
    //     }else{
    //         layer.open({
    //             type: 1,
    //             area: ['30%', '400px'],
    //             fix: false,
    //             maxmin: true,
    //             shade: 0.3,
    //             title: "修改运费",
    //             content: $("#scanCodeHtml").html(),
    //             btn: ['保存', '关闭'],
    //             shadeClose: true,
    //             success: function (layero, index) {
    //                 $("#freightTotal").val(row.freightTotal)
    //             },
    //             yes: function (index, layero){
    //                 var data = {invoiceId: row.invoiceId, amount: $("#freightTotal").val(),status: row.receiveVbillstatus, type: 0};
    //                 editReceive(data);
    //             },
    //             cancel: function(index) {
    //                 return true;
    //             }
    //         })
    //     }
    // }

    function getDailList(invoiceId) {
        let html=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'>`;
        
        if(invoiceId){
            $.ajax({
                url: ctx + "settlement_manage/check/log",
                method: 'get',
                dataType: "json",
                data: {invoiceId},
                async : false,
                success: function (result) {
                    if(result.data.length>0){
                        html+=`<table class='custom-tab tab table'> <thead style='background: #f4f6f7;'><tr>
                                <th>发货单号</th>
                                <th>审核状态</th>
                                <th>审核人</th>
                                <th>审核时间</th>

                            </tr></thead><tbody> `;

                        result.data.forEach(res=>{
                            html+=`<tr>
                                <td>`+res.invoiceNo+`</td>
                                <td>`+(res.checkStatus == 1 ? "审核通过" : "审核失败")+`</td>
                                <td>`+res.checkUserName+`</td>
                                <td>`+res.checkDate+`</td>

                            </tr>`
                        })

                        html+=`</tbody></table>`;
                        
                    }else{
                        html+=`暂无数据`;
                    }
                }
            });
        }
        
        html+=`</div></div></div>`;
        return html
    }

</script>

</body>
</html>