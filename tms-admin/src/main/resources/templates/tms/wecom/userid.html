<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('企微用户')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .flex {
            display: flex;
            algin-items: center;
            just-content: space-between;
            margin-bottom: 5px;
        }

        .flex_left {
            width: 100px;
            text-align: right;
            line-height: 26px;
        }

        .flex_right {
            min-width: 0;
            flex: 1;
            line-height: 26px;
        }

        .tooltip-inner {
            max-width: 240px;
            text-align: left;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <input name="loginName" class="form-control"
                               placeholder="TMS登录名" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="userName" class="form-control"
                               placeholder="姓名" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="wecomUserid" class="form-control"
                               placeholder="企微ID" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select class="form-control selectpicker" name="params[search]" data-none-selected-text="特殊搜索">
                            <option></option>
                            <option value="noId">无企微ID</option>
                            <option value="sameId">重复绑定同一企微ID</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success single disabled" onclick="manualBind()">
                <i class="fa fa-hand-pointer-o"></i> 手动绑定
            </a>
            <a class="btn btn-warning multiple disabled" onclick="autoBind()" data-toggle="tooltip"
               data-html="true" data-container="body" data-delay='{"show":0,"hide":500}'
               title="<div>优先以手机号码匹配，一致时直接绑定；<br>未匹配到时，以TMS登录名匹配，并判断姓名是否一致</div>">
                <i class="fa fa-laptop"></i> 快速匹配
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    function searchx() {
        var data = {};
        //data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
        $.table.search('role-form', data);
    }
    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }
    let queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()
    });

    function initTable() {
        var options = {
            url: ctx + "wecom/userid-list",
            queryParams: queryParams,
            uniqueId: 'userId',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: false,
            showExport: false,
            onPostBody: function () {

            },
            onRefresh: function (params) {

            },
            onCheck: function (row, $element) {

            },
            onUncheck: function (row, $element) {

            },
            onCheckAll: function (rowsAfter) {

            },
            onUncheckAll: function () {

            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "";
                    }
                },
                {field: 'loginName', title: 'TMS登录名', width: 120, formatter: function(value,row,index){
                    return value + (row.delFlag == '2' ? ' (已删除)':'');
                    }},
                {field: 'userName', title: '姓名', width: 120},
                {field: 'phonenumber', title: '手机号', width: 120},
                {field: 'wecomUserid', title: '企微ID', width: 120}
            ]
        };

        $.table.init(options);
    }

    function manualBind() {
        var row = $.btTable.bootstrapTable('getSelections')[0];
        layer.open({
            type: 1,
            title: '指定企微ID',
            area: ['400px', '300px'],
            skin: '',
            content: `<form id="theForm" style="padding: 10px">
                <input type="hidden" name="userId" value="${row.userId}">
                <div class="flex">
                <span class="flex_left">TMS登录名：</span>
                <span class="flex_right">${row.loginName}</span>
                </div>
                <div class="flex">
                <span class="flex_left">姓名：</span>
                <span class="flex_right">${row.userName}</span>
                </div>
                <div class="flex">
                <span class="flex_left">手机：</span>
                <span class="flex_right">${row.phonenumber}</span>
                </div>
                <div class="flex">
                <span class="flex_left">企微ID：</span>
                <span class="flex_right"><input class="form-control" name="wecomUserid" placeholder="注意大小写" value="${row.wecomUserid||''}"></span>
                </div>
                </form>`,
            btn: ['保存','取消'],
            yes: function (index, layero) {
                let confirm = "确定提交吗？"
                let wecomUserid = layero.find('[name=wecomUserid]').val().trim();
                layero.find('[name=wecomUserid]').val(wecomUserid);
                if (!wecomUserid) {
                    if (row.wecomUserid) {
                        confirm = "确认删除该企微ID绑定吗？";
                    } else {
                        $.modal.msgWarning("没有可提交数据");
                        return
                    }
                }
                $.modal.confirm(confirm, function(){
                    $.operate.submit(ctx + "wecom/save-userid", "post", "json", $('#theForm').serialize(), function(result){
                        if (result.code == 0) {
                            layer.close(index)
                        }
                    });
                })
            }
        })
    }

    function autoBind() {
        var rows = $.btTable.bootstrapTable('getSelections');
        const arr = [];
        for (let i = 0; i < rows.length; i++) {
            arr.push(rows[i].userId)
        }
        $.modal.confirm("确认匹配该" + rows.length + "个账户吗？", function(){
            $.operate.submit(ctx + "wecom/auto-bind", "post", "json", {userIds:arr.join(',')}, function(result){
                if (result.code == 0) {
                    setTimeout(function(){
                        var dataArr = result.data;
                        var tmp = []
                        for (let i = 0; i < dataArr.length; i++) {
                            tmp.push(rows[i].userName,"(",rows[i].loginName,")", "：", dataArr[i].code == 0 ? "绑定成功" : "绑定失败，" + dataArr[i].msg, "<br>")
                        }
                        $.modal.alertSuccess(tmp.join(''));
                    }, 200)
                }
            });
        });
    }
</script>
</body>
</html>