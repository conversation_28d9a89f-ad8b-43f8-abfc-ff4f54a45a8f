<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('配置')"/>
    <style type="text/css">
        .checkbox {
            padding-top: 3px !important;
        }
        .checkbox input {
            margin: 0;
        }
        .checkbox label {
            border: 1px #1BA356 dotted;
            border-radius: 3px;
            padding-top: 2.5px;
            padding-right: 3px;
        }
        .dis label {
            background-color: #ddd;
        }
        .flexRow {
            display: flex;
            justify-content: space-between;
        }
        .col1 { width: 130px; margin-right: 5px; }
        .col2 { width: 100px; margin-right: 5px; }
        .col3 { flex: 1; }
        .col4 { width: 170px; margin-left: 5px; }
        .col5 { width: 170px; margin-left: 5px; }
    </style>
</head>
<body>
    <pre>
    【exp:{字段}】 表达式语法 | 【from:字段1:字段2】 关联语法，字段1要求typeahead
    Typeahead：ctx后的相对路径:要展示的字段，默认查询参数:kw，返回结果为纯列表[{字段:值}]
    仅使用后端集成的时候：只有系统字段、摘要有效
    其他字段如果涉及userid，未能关联用户
    </pre>
    <div id="form1" class="form-horizontal m">
        <div class="flexRow">
            <div class="col1" style="text-align: right">模板字段</div>
            <div class="col2">系统字段</div>
            <div class="col3">禁视禁改[AE=DIV+H][VAE=NONE]</div>
            <div class="col4">Typeahead</div>
            <div class="col5">前端动态生成规则</div>
        </div>
    </div>
    <div id="form2" class="form-horizontal m">

    </div>
    <div style="position:fixed;top: 5px;right:5px;">
        <input type="button" class="btn btn-success btn-sm" value="追加其它字段" onclick="addOtherColumn()">
        <input type="button" class="btn btn-success btn-sm" value="同步下拉框" onclick="syncOptions()">
    </div>

<th:block th:include="include :: footer"/>
<script th:src="@{'/js/wecom.js'}"></script>
<script>
    var template;
    $(function () {
        $.ajax({
            url: ctx + 'wecom-sp/template?templateId=[[${param.templateId}]]&biz=[[${param.biz}]]',
            cache: false,
            success: function(result) {
                if (result.code == 0) {
                    template = JSON.parse(result.data.templateJson);
                    console.log(template)
                    let controls = template.template_content.controls;
                    showList(controls, 'form1')
                    console.log(result.data.keyReflectList);
                    fillReflect(result.data.keyReflectList);
                    console.log(result.data.otherColumns)
                    fillOther(result.data.otherColumns);

                } else {
                    $.modal.msgError(result.msg)
                }
            }
        })
    })

    function commonAttr(item, isOther) {
        item = item || {}
        var tmp = [];
        tmp.push(`<div class="col3">`)
        tmp.push(`    <div class="checkbox">`)
        tmp.push(`        <label title="列表数据（仅select）"><input type="checkbox" field="l"${item.l==1?' checked':''}>L</label>`)
        tmp.push(`        <label title="精确查询（未实现:范围）"><input ${isOther ? "disabled" : ""} type="checkbox" field="s"${item.s==1?' checked':''}>S</label>`)
        tmp.push(`        <label title="模糊查询（未实现:范围）"><input ${isOther ? "disabled" : ""} type="checkbox" field="s2"${item.s2==1?' checked':''}>S2</label>`)
        tmp.push(`<span class="dis">`)
        tmp.push(`        <label title="新增展示成div，有hidden"><input type="checkbox" field="a"${item.a==1?' checked':''}>A</label>`)
        tmp.push(`        <label title="修改展示成div，有hidden"><input type="checkbox" field="e"${item.e==1?' checked':''}>E</label>`)
        tmp.push(`        <label title="新增时看不到"><input type="checkbox" field="va"${item.va==1?' checked':''}>VA</label>`)
        tmp.push(`        <label title="编辑时看不到"><input type="checkbox" field="ve"${item.ve==1?' checked':''}>VE</label>`)
        tmp.push(`</span>`)
        tmp.push(`        <label title="datalist，记录后下次联想展示"><input type="checkbox" field="dl"${item.dl==1?' checked':''}>DL</label>`)
        tmp.push(`        <label title="vae选中时生成hidden"><input type="checkbox" field="h"${item.h==1?' checked':''}>H</label>`)
        tmp.push(`        <label title="摘要，最多点3个"><input type="checkbox" field="zy"${item.zy==1?' checked':''}>Z</label>`)
        tmp.push(`    </div>`)
        tmp.push(`</div>`)
        tmp.push(`<div class="col4">
            <input class="form-control" field="typeahead" value="${item.typeahead||''}"/>
        </div>`)
        tmp.push(`<div class="col5">
            <input class="form-control" field="genRule" value="${item.genRule||''}"/>
        </div>`);
        return tmp.join('')
    }

    function showList(controls, targetId, rank, pId) {
        rank = rank || 1;
        pId = pId || '';
        for (let i = 0; i < controls.length; i++) {
            var controlId = controls[i].property.id;
            var control = controls[i].property.control;
            var domid = controlId.replace(/-/g, '');
            if (control == 'Table') {
                $("#" + targetId).append(`
                    <div class="flexRow" style="background-color: #efefef">
                        <label class="col1 control-label" title="${controlId}">${text_zh(controls[i].property.title)}</label>
                        <div class="col2">
                            <input class="form-control" label="${text_zh(controls[i].property.title)}" control="${control}" key-id="${controlId}" rank="${rank}" seq="${i}" p_id="${pId}">
                        </div>
                        ${commonAttr()}
                    </div>
                    <div id="${domid}" table-area="1" style="padding-top:0px;border: 1px #ddd dotted;background-color: #f5f5f5"></div>
                `);
                showList(controls[i].config.table.children, domid, rank + 1, controlId);
                console.log('sum_field', controls[i].config.table.sum_field)
                for (let j = 0; j < controls[i].config.table.sum_field.length; j++) {
                    let {id,title,control:control2} = controls[i].config.table.sum_field[j];
                    //var col = {label:text_zh(title)}
                    $("#" + targetId).append(`<div class="flexRow">
                        <label class="col1 control-label">* ${text_zh(title)}</label>
                        <div class="col2">
                            <input class="form-control" label="${text_zh(title)}" control="${control2}" key-id="sum-${id}" rank="${rank}" seq="${i}" p_id="${pId}">
                        </div>
                        ${commonAttr()}
                    </div>`);
                }
            } else if (control == 'Tips') {
                $("#" + targetId).append(`<div class="flexRow">
                    <label class="col1 control-label">${text_zh(controls[i].property.title)}</label>
                    <div class="col2">
                        ${text_zh(controls[i].property.placeholder)}
                    </div>
                </div>`);
            } else {
                let dict = 0;
                let multi = 0;
                let type = '';
                if (control == 'Selector') {
                    dict = 1;
                    if (controls[i].config.selector.type == 'multi') {
                        multi = 1; // 多选下拉
                    }
                    type = controls[i].config.selector.type;
                } else if (control == 'Date') {
                    type = controls[i].config.date.type;
                } else {
                    // 还有哪些特殊类型
                }
                $("#" + targetId).append(`<div class="flexRow">
                    <label class="col1 control-label" title="${controlId}">${text_zh(controls[i].property.title)}</label>
                    <div class="col2">
                        <input class="form-control" label="${text_zh(controls[i].property.title)}" control="${control}" key-id="${controlId}" rank="${rank}" seq="${i}" p_id="${pId}" dict="${dict}" multi="${multi}" type="${type}">
                    </div>
                    ${commonAttr()}
                </div>`);
            }
        }
    }

    function fillReflect(list) {
        // 从系统字段定位整行，之后将各属性赋值给各[field]
        for (let i = 0; i < list.length; i++) {
            $('[key-id="'+list[i].keyId+'"]').val(list[i].field);
            var row = $('[key-id="'+list[i].keyId+'"]').parent().parent(); // row
            row.children().each(function () {
                if ($(this).attr('table-area')) {
                    return true;
                }
                $(this).find('[field]').each(function() { // Table会找到子集的field
                    var field = $(this).attr("field");
                    if ($(this).prop('type') == 'checkbox') {
                        $(this).prop("checked", list[i][field] == 1);
                    } else {
                        $(this).val(list[i][field]);
                    }
                })
            })
        }
    }

    function fillOther(list) {
        for (let i = 0; i < list.length; i++) {
            addOtherColumn(list[i])
        }
        window.scrollTo(0, 0)
    }

    function submitHandler(index, layero, close) {
        var list = []
        $('[control]').each(function(){
            let control = $(this).attr('control');
            let label = $(this).attr('label');
            let type = $(this).attr('type');
            let keyId = $(this).attr('key-id');
            let rank = $(this).attr('rank') * 1;
            let field = $(this).val().trim();//.toUpperCase();add自动生成后可放开
            let seq = $(this).attr('seq') * 1;
            let pId = $(this).attr('p_id') || '';
            let dict = $(this).attr('dict') * 1;
            if (isNaN(dict)) {
                dict = 0;
            }
            let multi = $(this).attr("multi") * 1;
            if (isNaN(multi)) {
                multi = 0;
            }
            var row = $(this).parent().parent();
            if (field) {
                var item = {keyId:keyId,label:label, control:control, type:type, field:field, templateId:'[[${param.templateId}]]', biz:'[[${param.biz}]]', rank:rank, seq:seq, pId:pId, dict:dict, multi:multi}
                row.find('[field]').each(function() { // Table类会找到内部明细的field控件
                    let f = $(this).attr("field")
                    if (item.hasOwnProperty(f)) {
                        return false;
                    }
                    if ($(this).prop('type') == 'checkbox') {
                        item[f] = $(this).prop("checked")?1:0;
                    } else {
                        item[f] = $(this).val();
                    }
                })
                list.push(item)
            }
        });
        $('[other-column]').each(function (){
            var es = $(this).find(":input");
            var label = es.eq(0).val()
            var field = es.eq(1).val();//.toUpperCase()add自动生成后可放开
            var row = $(this);
            if (label && field) {
                var item = {label: label, field: field, flag: 'other', templateId:'[[${param.templateId}]]', biz:'[[${param.biz}]]'}
                row.find('[field]').each(function() {
                    if ($(this).prop('type') == 'checkbox') {
                        item[$(this).attr("field")] = $(this).prop("checked")?1:0;
                    } else {
                        item[$(this).attr("field")] = $(this).val();
                    }
                })
                list.push(item);
            }
        })
        console.log(list)
        if (list.length > 0) {
            $.modal.confirm("确认提交吗？", function () {
                $.ajax({
                    url: ctx + 'wecom-sp/save-reflect',
                    method: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify(list),
                    success: function (result) {
                        if (result.code == 0) {
                            close(index)
                        } else {
                            $.modal.msgError(result.msg)
                        }
                    }
                })
            })
        } else {
            $.modal.msgWarning("未找到可提交数据");
        }
    }

    function otherColumn(col) {
        return `<div class="flexRow" other-column>
                        <label class="col1"><input class="form-control" style="text-align: right" value="${col.label||''}"></label>
                        <div class="col2"><input class="form-control" value="${col.field||''}"></div>
                        ${commonAttr(col, 1)}
                    </div>`
    }

    function addOtherColumn(col) {
        col = col || {h:1}
        $("#form2").append(otherColumn(col));
        var ele = document.getElementsByTagName("body")[0];
        window.scrollTo(0, ele.scrollHeight)
    }

    function getOptions(controls) {
        var opts = []
        for (let i = 0; i < controls.length; i++) {
            console.log(controls[i])
            if (controls[i].property.control == 'Selector') {
                var options = controls[i].config.selector.options;
                for (let j = 0; j < options.length; j++) {
                    var opt = {value:options[j].key, label:text_zh(options[j].value), ofControl:controls[i].property.id};
                    opts.push(opt)
                }
            } else if (controls[i].property.control == 'Table') {
                var opts2 = getOptions(controls[i].config.table.children);
                opts.push(...opts2);
            }
        }
        return opts;
    }

    function syncOptions() {
        /**/
        layer.open({
            type: 1,
            area: ['900px', '400px'],
            fix: false,
            skin: 'add-class',
            //不固定
            maxmin: true,
            shade: 0.3,
            zIndex: 1,
            title: '字典项绑定',
            content: `
                <div id="form3" class="form-horizontal m">
                    <div class="form-group">
                        <div class="col-sm-5 col-sm-offset-1">模板选项</div>
                        <div class="col-sm-3">本地字典项(同一字段注意唯一性)</div>
                    </div>
                </div>
            `,
            btn: ['保存', '取消'],
            //btn: ['提交审批', '取消'],
            // 弹层外区域关闭
            shadeClose: false,
            success: function (layero) {
                var controls = template.template_content.controls;
                var controlOption = function (controlList) {
                    for (let i = 0; i < controlList.length; i++) {
                        if (controlList[i].property.control == 'Selector') {
                            var title = text_zh(controlList[i].property.title);
                            $("#form3").append(`<div class="form-group">
                                    <div class="col-sm-12" style="background-color: #dddddd;padding: 3px;text-align: center;color:blue;">字段: ${title}</div>
                                </div>`)
                            var options = controlList[i].config.selector.options;
                            for (let j = 0; j < options.length; j++) {
                                // var opt = {value:options[j].key, label:text_zh(options[j].value), ofControl:controlList[i].property.id};
                                $("#form3").append(`<div class="form-group">
                                    <div class="col-sm-5 col-sm-offset-1">${text_zh(options[j].value)}</div>
                                    <div class="col-sm-3"><input type="text" class="form-control" key-id="${options[j].key}" label="${text_zh(options[j].value)}" of-control="${controlList[i].property.id}" biz-code/></div>
                                </div>`)
                            }
                            console.log(options)
                        } else if (controlList[i].property.control == 'Table') {
                            controlOption(controlList[i].config.table.children)
                        }
                    }
                }

                controlOption(controls);

                $.ajax({
                    url: ctx + 'wecom-sp/list-opt-dict?templateId=[[${param.templateId}]]&biz=[[${param.biz}]]',
                    cache: false,
                    success: function(result) {
                        if (result.code == 0) {
                            let list = result.data;
                            for (let i = 0; i < list.length; i++) {
                                $('[key-id="'+list[i].value+'"]').val(list[i].bizCode)
                            }
                        } else {
                            $.modal.msgError(result.msg)
                        }
                    }
                })
            },
            btn1: function (index, layero) {
                var opts = []
                $('[biz-code]').each(function(){
                    let $e = $(this);
                    var opt = {value:$e.attr("key-id"), label:$e.attr("label"), ofControl:$e.attr("of-control"), bizCode:$e.val().trim()};
                    opts.push(opt);
                })
                console.log(opts)
                if (opts.length > 0) {
                    $.ajax({
                        url: ctx + 'wecom-sp/save-opt-dict?templateId=[[${param.templateId}]]&biz=[[${param.biz}]]',
                        contentType: 'application/json',
                        data: JSON.stringify(opts),
                        type: 'post',
                        success: function(result) {
                            if (result.code == 0) {
                                $.modal.msgSuccess("同步成功");
                                layer.close(index)
                            } else {
                                $.modal.msgError(result.msg)
                            }
                        }
                    })
                }
            },
            btn2: function (index, layero) {

                //return false; // 与btn1的关闭层逻辑不一致
            }
        });
    }
</script>

</body>
</html>