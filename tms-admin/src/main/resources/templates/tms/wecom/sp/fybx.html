<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请')"/>

    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-editable-select/jquery-editable-select.min.css}" media="all" type="text/css"/>

    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .add-class .layui-layer-btn1 {
            border-color: #e38d13;
            background-color: #e38d13;
            color: #fff;
        }

        .proofClass .layui-layer-btn1 {
            border-color: #e30000;
            background-color: #e30000;
            color: #fff;
        }
        .flex {
            display: flex;

            margin-bottom: 5px;
        }

        .flex_left {
            width: 140px;
            text-align: right;
            padding: 5px 10px 0 10px;
        }

        .flex_right {
            min-width: 0;
            flex: 1;
        }

        .file-drop-zone {
            height: auto !important;
            min-height: auto !important;
        }

        .dyna-form .row {
            margin-right: -10px;
        }
        div.form-control {
            border: 1px solid #bbb;
            line-height: 22px;
            border-radius: 3px;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <input name="SP_NO" class="form-control" type="text"
                               placeholder="审批单号" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select name="STATUS" class="form-control selectpicker" data-none-selected-text="状态">
                            <option></option>
                            <option value="0">新建</option>
                            <option value="1">审批中</option>
                            <option value="2">已通过</option>
                            <option value="3">已驳回</option>
                            <option value="4">已撤销</option>
                            <option value="20">已核销</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="KHMC" class="form-control" type="text"
                               placeholder="客户名称" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select name="fpcq" class="form-control valid selectpicker"
                                aria-invalid="false" data-none-selected-text="待上传发票">
                            <option></option>
                            <option value="1">待上传</option>
                            <option value="2">超期未上传</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-4">
                            <input name="sum_fyje" class="form-control" type="text"
                                   placeholder="报销金额(精确数字)" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="userName" class="form-control" type="text"
                               placeholder="申请人" autocomplete="off" aria-required="true">
                    </div>
                </div>
                <div class="row" style="margin-top: 5px">
                    <div class="col-md-4 col-sm-12">
                        <input name="bxsy" class="form-control" type="text"
                               placeholder="报销事由" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select name="closed" class="form-control selectpicker" data-none-selected-text="关闭状态">
                            <option value=""></option>
                            <option value="0" selected>正常</option>
                            <option value="1">未核销已关闭</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-md-offset-4 col-sm-4">
                        <div style="text-align: center">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-info" onclick="settingPanel()" shiro:hasPermission="wecom:sp:setting">
                <i class="fa fa-cog"></i> 配置
            </a>
            <a class="btn btn-primary" onclick="add('0')">
                <i class="fa fa-plus"></i> 业务类申请
            </a>
            <a class="btn btn-info" onclick="add('1')">
                <i class="fa fa-plus"></i> 非业务类申请
            </a>
            <a class="btn btn-danger single disabled" onclick="del()">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-success single disabled" onclick="view()">
                <i class="fa fa-eye"></i> 查看明细
            </a>
            <a class="btn btn-info single disabled" onclick="process()">
                <i class="fa fa-laptop"></i> 审批进度
            </a>
            <a class="btn btn-warning single disabled" onclick="dynaPrint()">
                <i class="fa fa-print"></i> 打印
            </a>
            <!--<a class="btn" onclick="test()">Test</a>-->
            <a class="btn btn-danger single disabled" shiro:hasPermission="sp:fybx:up_proof" onclick="vPanelUploadProof()">
                <i class="fa fa-dollar"></i> 财务核销
            </a>
            <a class="btn btn-danger multiple disabled" shiro:hasPermission="sp:fybx:close" onclick="closeFun()">
                <i class="fa fa-close"></i> 关闭
            </a>

            <a class="btn btn-success" th:if="${isAdmin}" onclick="exportFybx()">
                <i class="fa fa-download"></i> 导出(管理员)
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
<script th:src="@{/ajax/libs/jquery-editable-select/jquery-editable-select.js}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput/plugins/piexif.min.js}"></script>
<script th:src="@{'/js/wecom.js?t=20250603'}"></script>
<style type="text/css">
    .file-preview {
        padding: 0px!important;
        border: none!important;
    }
    .file-drop-zone {
        margin: 0!important;
        overflow: auto;
        /*min-height: 200px!important;*/
    }
    .file-thumb-progress .progress {
        margin-bottom: 5px;
    }
    .file-drop-zone-title {
        padding: 0px 10px !important;
    }
</style>
<script th:inline="javascript">
    var prefix = ctx + "fybx";
    var templateId = [[${templateId}]];
    var biz = [[${biz}]];
    //收款方式
    var receivable_method = [];
    //发票类型
    var billing_type = [];
    //公司名称
    var bala_corp = [];
    //是否开票
    var if_billing = [];
    //收款单据状态
    var vbillstatus = [];
    var submitUrl = ctx + "fybx/submit"

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    var dynaColumns = [[${keyReflectList}]];
    var template = JSON.parse([[${templateJson}]]);
    var otherColumns = [[${otherColumns}]];
    var optDictList = [[${optDictList}]];
    var dictBalaCorp = [[${@dict.getTypeAll('bala_corp')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()
    });

    function initTable() {
        var options = {
            url: ctx + "fybx/dyna-list?templateId=[(${templateId})]&biz=[(${biz})]",
            queryParams: queryParams,
            uniqueId: 'ID',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                clearTotal()
                merge_footer();
                getAmountCount();
            },
            onRefresh: function (params) {
                clearTotal();
            },
            onCheck: function (row, $element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "报销金额：<nobr id='totalSumFyje'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：报销金额：<nobr id='sumSumFyje'>¥0.00</nobr>";
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    field: 'ID',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:outBusinessMoney:moneyAdd')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="营业外收入登记" onclick="moneyAdd(\'' +row.ID + '\')"><i class="fa fa-dollar"" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    }
                },
                {field: 'BT', title: '标题'},
                {field: 'TYPE',title: '类别', formatter: function (value, row, index){
                    if (value == '1') {
                        return '个人借款'
                    } else if (value == '2') {
                        return '对公借款'
                    } else if (value == '3') {
                        return '对公付款'
                    } else if (value == '4') {
                        return '费用报销'
                    }
                }},
                {field: 'STATUS', title: '状态', formatter: function (value, row, index) {
                    let text;
                    if (value == 0) {
                        text = "新建"
                    } else if (value == 1) {
                        text = "审批中"
                    } else if (value == 2) {
                        text = "已通过"//；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
                    } else if (value == 3) {
                        text = "已驳回"
                    } else if (value == 4) {
                        text = "已撤销"
                    } else if (value == 20) { // tms自定状态码
                        text = "<a href='javascript:vFybxProof(\""+row.ID+"\")' title='点击查看核销凭证'>已核销</a>"
                    }
                    if (row.CLOSED == 1) {
                        text = text + '(已关闭)'
                    }
                    return text
                }},
                {field: 'SP_NO', title: '审批单号'},
                {field: 'SUM_FYJE', title: '报销金额', align:'right',formatter: function(value,row,index) {
                        return (value*1).toFixed(2);
                    }},
                {field: 'OUT_MONEY', title: '营业外收入', align:'right',formatter: function(value,row,index) {
                        let val = (value*1).toFixed(2);
                        if(val == 0){
                            return val;
                        }
                        return `<a onclick="outBusinessJump('`+row.ID+`')">`+val+`</a>`;
                    }},
                {field: 'SFYP_NM', title: '是否有票', formatter: function(value,row,index) {
                    if (row.SFYP != null) {//新数据
                        //if (row.YJDPSJ == 1) {//预计到票时间：1=立即；2=一周内；3=半月内；4=一月内
                            if (row.FPSC_LIST.length > 0) {
                                return "<a href='javascript:;' onclick='fpsc("+index+")'>已上传</a>";
                            } else {
                                return "<a href='javascript:;' onclick='fpsc("+index+")'>待上传(" + row.YJDPSJ_NM + ")</a>"
                            }
                        //}
                    }
                    return value
                    }},
                {field: 'BXSY', title: '报销事由',formatter: function(value,row,index) {
                    return $.table.tooltip(value,20);
                }},
                {field: 'USER_NAME', title: '申请人'},
                {field: 'KHMC', title: '客户名称'},
                {field: 'SSGS', title: '所属公司'}
                //{field: 'KHH', title: '开户行'}
            ]
        };

        $.table.init(options);
    }

    function fpsc(index) {
        let row = $.btTable.bootstrapTable('getData')[index];
        let tmp = []
        tmp.push("<form id='fpform' style='padding: 5px'>")
        if (row.FPSC_LIST.length > 0) {
            tmp.push('<div class="form-control imgPreview" style="height:auto;min-height:26px;margin-bottom: 10px">')
            for (let i = 0; i < row.FPSC_LIST.length; i++) {
                let f = row.FPSC_LIST[i];
                tmp.push('<span style="margin-right:10px;">'); // uploaded属性，代表已上传的附件
                var fName = f.fileName.toLowerCase();
                if (fName.endsWith(".jpg") || fName.endsWith(".png") || fName.endsWith(".gif") || fName.endsWith(".bmp")) {
                    tmp.push(' <img style="width:70px; height:50px" src="', f.filePath, '" alt="', f.fileName, '" title="', f.fileName, '">');
                } else {
                    tmp.push(' <a href="', f.filePath, '" target="_blank">', f.fileName, '</a>')
                }
                //tmp.push('<a href="', fileList[i].filePath, '" target="_blank">', fileList[i].fileName, '</a>');
                //tmp.push(' <a href="javascript:;" class="fa fa-times" title="删除图片"' +
                //    ' style="display:inline-block;width:14px;height:14px;line-height:13px;color:red;text-align:center;border-radius: 7px;border:1px red solid" onclick="tmpDelFile(\'',del_field_name,'\',\'',fileList[i].fileId,'\',this)">' +
                //    '</a>')
                tmp.push('</span>')
            }
            tmp.push('</div>')
        }
        tmp.push('<input type="file" control="File" name="fp" multiple />')
        tmp.push('</form>')

        layer.open({
            type: 1,
            area: ['500px', '500px'],
            fix: false,
            skin: 'none',
            //不固定
            maxmin: true,
            shade: 0.3,
            zIndex: 3,
            title: '发票上传',
            content: tmp.join(''),
            offset: 100,
            btn: ['保存', '取消'],
            shadeClose: false,
            success: function (layero) {
                polyfill("a", layero)
            },
            btn1: function (index, layero) {
                let uploaded = $('[name=fp_hide_tid]').val();
                if (!uploaded) {
                    $.modal.msgError("未上传文件，无需保存")
                    return;
                }
                $.ajax({
                    url: ctx + "fybx/fpsc",
                    type: 'post',
                    data: {id:row.ID, fpsc: uploaded},
                    success: function(result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                            $.table.refresh();
                            layer.close(index)
                        } else {
                            $.modal.msgError(result.msg)
                        }
                    },
                    error: function (res) {
                        $.modal.msgError(res.responseText)
                    }
                })
            }
        });
    }

    function searchx() {
        var data = {};
        //data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
        $.table.search('role-form', data);
    }
    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }
    function settingPanel() {
        showSpSettings(templateId, biz);
    }

    function del() {
        var id = $.table.selectColumns("ID")[0];
        var status = $.table.selectColumns("STATUS")[0];
        if (status != 0 && status != 3) {
            $.modal.msgWarning("只能删除新建或驳回状态下的单据");
            return;
        }
        $.modal.confirm("确认删除该记录？", function () {
            $.ajax({
                url: ctx + 'wecom-sp/del[(${biz})]/' + id,
                cache: false,
                type: 'json',
                success: function (ar) {
                    if (ar.code == 0) {
                        $.table.refresh();
                    } else {
                        $.modal.msgError(ar.msg)
                    }
                }
            })
        });
    }

    function add(gscm) {
        var tables = [] // 临时存放表格html
        var formHtml = [];
        formHtml.push(`<form id="form-add" class="form-horizontal dyna-form"><div class="panel-body" style="padding:5px 20px 0px;">`);
        formHtml.push(`<div class="row">`); // row开始
        /*formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">标题 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <input class="form-control" name="bt" autocomplete="off">
                            </div>
                        </div>
                    </div>`);*/
        for (var i = 0; i < otherColumns.length; i++) {
            formHtml.push(asHidden(otherColumns[i]))
        }
        let controls = template.template_content.controls;
        for (var i = 0; i < controls.length; i++) {
            let c = controls[i];
            //console.log(c)
            if (c.property.control == 'Tips') {
                formHtml.push(asTips(c))
            } else {
                var setting = findSetting(c.property.id)
                if (setting != null) {
                    if (c.property.control == 'Table') {
                        tables.push(asAddItem(c, setting)); // 表格数据临时存放到tables
                    } else {
                        formHtml.push(asAddItem(c, setting))
                    }
                }
            }
        }

        /*formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">客户名称 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <input class="form-control" name="khmc" placeholder="输入关键字，字数越多越精确" autocomplete="off" required />
                                <input type="hidden" name="customer_id"/>
                            </div>
                        </div>
                    </div>`);
        formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">所属公司 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <div class="form-control" id="ssgs"></div>
                                <input type="hidden" name="ssgs">
                                <input type="hidden" name="bala_corp">
                            </div>
                        </div>
                    </div>`);
        formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">部门 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <div class="form-control" id="bm"></div>
                                <input type="hidden" name="bm">
                                <input type="hidden" name="sales_dept">
                            </div>
                        </div>
                    </div>`);
        formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">报销类型 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <select class="form-control" name="bxlx" required>
                                    <option value=""></option>`);
        formHtml.push(asOptions(template.template_content.controls, 'item-1503317593875'))
        formHtml.push(`         </select>
                            </div>
                        </div>
                    </div>`);
        formHtml.push(`<div class="col-sm-12">
                        <div class="flex">
                            <label class="flex_left">报销事宜 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <textarea class="form-control" name="bxsy" autocomplete="off" required></textarea>
                            </div>
                        </div>
                    </div>`);
        // File标签不能加required，拖拽进来的文件检测不到
        formHtml.push(`<div class="col-sm-12">
                        <div class="flex">
                            <label class="flex_left">附件 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <input type="file" control="File" name="files" multiple />
                            </div>
                        </div>
                    </div>`);
        formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">收款单位（姓名） <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <input class="form-control" control="Text" name="skdw" autocomplete="off" required/>
                            </div>
                        </div>
                    </div>`);
        formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">开户行\\行号 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <input class="form-control" control="Text" name="khh" autocomplete="off" required/>
                            </div>
                        </div>
                    </div>`);
        formHtml.push(`<div class="col-sm-6">
                        <div class="flex">
                            <label class="flex_left">银行帐号 <span class="fcff3">*</span></label>
                            <div class="flex_right">
                                <input class="form-control" control="Text" name="yhzh" autocomplete="off" required/>
                            </div>
                        </div>
                    </div>`);*/
        formHtml.push(`</div>`); // row结束

        formHtml.push(...tables); // 在所有表单控件增加完毕后，增加表格控件

        // 明细
        /*formHtml.push(`<div class="panel-group" control="Table" name="dtl">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">报销明细<a style="float:right;color:#409eff" href="javascript:;" onclick="addMx('item-1503317853434')">追加</a></h4>
                        </div>
                        <div class="panel-collapse">
                            <table class="table table-condensed">
                                <thead>
                                    <tr>
                                        <th style="width:46px;text-align: center">№</th>
                                        <th style="width: 20%">费用类型 <span class="fcff3">*</span></th>
                                        <th style="width: 20%">发生日期 <span class="fcff3">*</span></th>
                                        <th style="width: 20%">费用金额 <span class="fcff3">*</span></th>
                                        <th>备注</th>
                                        <th style="width:30px"></th>
                                    </tr>
                                </thead>
                                <tbody id="item-1503317853434">`)
        formHtml.push(`<tr>
                 <td style="text-align: center">1</td>
                 <td><select class="form-control" name="fylx" control="Selector" required><option></option>${asOptions(findControl(template.template_content.controls, 'item-1503317853434').config.table.children, 'item-1503317870534')}</select></td>
                 <td><input type="text" name="fsrq" class="form-control" control="Date" autocomplete="off" required></td>
                 <td><input type="text" name="fyje" class="form-control" control="Number" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" required></td>
                 <td><input type="text" name="bz" class="form-control" autocomplete="off"></td>
                 <td style="text-align: center"><a href="javascript:;" onclick="rmv(this)"><i class="fa fa-remove" style="color:red"></i></a></td>
             </tr>`);
        formHtml.push(`</tbody>
                            </table>
                        </div>
                    </div>
                </div>`);
        */
        formHtml.push(`</div></form>`);
        var pics = []
        var curr_cust;
        var uploading = false;
        var cache = {}
        var fileArr = []
        //window.setInterval(function (){
        //    console.log(cache)
        //    console.log(fileArr)
        //}, 5000)

        layer.open({
            type: 1,
            area: ['900px', '600px'],
            fix: false,
            skin: 'add-class',
            //不固定
            maxmin: true,
            shade: 0.3,
            zIndex: 3,
            title: '新增付款申请',
            content: formHtml.join(''),
            btn: ['保存', '保存并提交审批', '取消'],
            //btn: ['提交审批', '取消'],
            // 弹层外区域关闭
            shadeClose: false,
            success: function (layero) {
                //$('[name="type"]').after('<i id="typehelp" class="fa fa-question-circle" style="position:absolute;right:-6px;top:6px;color:#409eff;z-index:2;"></i>');
                //$('#typehelp').popover({content:'<b>个人借款：</b>无票，提前跟公司预支费用，例如：需要出差，提前跟公司预借费用，有票据之后再申请费用报销冲抵借款、归还借款，后期会有流程关联；<br>' +
                //        '<b>对公借款：</b>先对公付款，后补票，后续申请收票核销；<br>' +
                //        '<b>对公付款：</b>有票，且收款方为对方公司账户；<br>' +
                //        '<b>费用报销：</b>内部员工，申请油卡充值或者申请对私付款，且提供付款凭证，例如停车费、差旅费等发票凭证',html:true,trigger:'hover',container:'body',delay:0})
                $('[name="type"]').val('4');
                $('[name="khmc"]').attr("placeholder", "输入关键字，字数越多越精确");
                $('[name="gscm"]').val(gscm)
                if (gscm != '0') {
                    $('[name="customer_id"]').remove()
                    $('[name="sales_dept"]').remove()
                    $('[name="gscm"]').find('option[value=0]').remove();
                    $('[name="gscm"]').after('<input type="hidden" name="khmc">'); // 自定义的khmc，原始的会被加hide标记，提交时被清空
                    $('[name="gscm"]').change(function(){
                        var khmc = $('[name="gscm"]').find('option:selected').text();
                        $('[name="khmc"]').val(khmc)
                    }).change()
                    //$('[name="khmc"]').closest('.col-sm-6').hide()
                    $('[name="bm"]').closest('.col-sm-6').remove()
                    var options = []
                    for (let i = 0; i < dictBalaCorp.length; i++) {
                        options.push('<option value="',dictBalaCorp[i].dictValue,'">',dictBalaCorp[i].dictLabel,'</option>')
                    }
                    $('[name=ssgs]').remove()
                    $('[name="bala_corp"]').replaceWith('<input name="ssgs" type="hidden">');
                    $('#ssgs').replaceWith('<select name="bala_corp" required class="form-control" onchange="$(\'[name=ssgs]\').val($(this).find(\'option:selected\').text())">' +
                        '<option></option>' + options.join("") + '</select>')
                } else {
                    $('[name="gscm"]').closest('.col-sm-6').hide()
                }
                polyfill('a')
                useKeyRelation({"GSCM":gscm});
            },
            btn1: function (index, layero) {
                commitx(index, false)
            },
            btn2: function (index, layero) {
               commitx(index, true)
               return false; // 与btn1的关闭层逻辑不一致
            }
        });
    }

    function view () {
        var id = $.table.selectColumns("ID")[0];
        $.ajax({
            url: ctx + 'wecom-sp/dyna-dtl?id=' + id + "&biz=[(${biz})]",
            cache: false,
            success: function(result) {
                if (result.code != 0) {
                    $.modal.msgError(result.msg)
                    return;
                }
                var tables = [] // 临时存放表格html
                var formHtml = [];
                var editable = result.data.STATUS == 0 || result.data.STATUS == 3;
                formHtml.push(`<form id="form-add" class="form-horizontal dyna-form view-form"><div style="padding:5px 20px 0px;">`);
                formHtml.push(`<input type="hidden" name="id" value="${id}"/>`)
                for (var i = 0; i < otherColumns.length; i++) {
                    formHtml.push(asHidden(otherColumns[i], result.data))
                }

                formHtml.push(`<div class="row">`); // row开始

                let controls = template.template_content.controls;
                for (var i = 0; i < controls.length; i++) {
                    let c = controls[i];
                    if (c.property.control == 'Tips') {
                        formHtml.push(asTips(c))
                    } else {
                        var setting = findSetting(c.property.id);
                        if (setting != null) {
                            if (c.property.control == 'Table') {
                                tables.push(editable ? asEditItem(c, setting, result.data) : asViewItem(c, setting, result.data)); // 表格数据临时存放到tables
                            } else {
                                formHtml.push(editable ? asEditItem(c, setting, result.data) : asViewItem(c, setting, result.data));
                            }
                        }
                    }
                }

                formHtml.push(`</div>`); // row结束

                formHtml.push(...tables); // 在所有表单控件增加完毕后，增加表格控件

                formHtml.push(`</div></form>`);
                layer.open({
                    type: 1,
                    area: ['900px', '600px'],
                    fix: false,
                    skin: 'add-class',
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    zIndex: 3,
                    title: '付款申请明细',
                    content: formHtml.join(''),
                    btn: editable ? ['保存','保存并提交审批','关闭']:['关闭'],
                    // 弹层外区域关闭
                    shadeClose: false,
                    success: function (layero) {
                        if (editable) {
                            //$('[name="type"]').after('<i id="typehelp" class="fa fa-question-circle" style="position:absolute;right:-6px;top:6px;color:#409eff;z-index:2;"></i>');
                            //$('#typehelp').popover({content:'<b>个人借款：</b>无票，提前跟公司预支费用，例如：需要出差，提前跟公司预借费用，有票据之后再申请费用报销冲抵借款、归还借款，后期会有流程关联；<br>' +
                            //        '<b>对公借款：</b>先对公付款，后补票，后续申请收票核销；<br>' +
                            //        '<b>对公付款：</b>有票，且收款方为对方公司账户；<br>' +
                            //        '<b>费用报销：</b>内部员工，申请油卡充值或者申请对私付款，且提供付款凭证，例如停车费、差旅费等发票凭证',html:true,trigger:'hover',container:'body',delay:0})
                        }
                        if (result.data.GSCM != '0') {
                            if (editable) {
                                $('[name="customer_id"]').remove()
                                $('[name="gscm"]').val(result.data.GSCM)
                                $('[name="gscm"]').find('option[value=0]').remove();
                                $('[name="gscm"]').after('<input type="hidden" name="khmc">'); // 自定义的khmc，原始的会被加hide标记，提交时被清空
                                $('[name="gscm"]').change(function(){
                                    var khmc = $('[name="gscm"]').find('option:selected').text();
                                    $('[name="khmc"]').val(khmc)
                                }).change()
                                $('[name="sales_dept"]').remove()
                                $('[name="bm"]').closest('.col-sm-6').remove()

                                var options = []
                                for (let i = 0; i < dictBalaCorp.length; i++) {
                                    options.push('<option value="', dictBalaCorp[i].dictValue, '"', result.data.BALA_CORP == dictBalaCorp[i].dictValue ? ' selected' : '', '>', dictBalaCorp[i].dictLabel, '</option>')
                                }
                                $('[name=ssgs]').remove()
                                $('[name="bala_corp"]').replaceWith('<input name="ssgs" type="hidden" value="'+result.data.SSGS+'">');
                                $('#ssgs').replaceWith('<select name="bala_corp" required class="form-control" onchange="$(\'[name=ssgs]\').val($(this).find(\'option:selected\').text())">' +
                                    '<option></option>' + options.join("") + '</select>')
                            } else {
                                $('#khmc').closest('.col-sm-6').remove()
                                $('#bm').closest('.col-sm-6').remove()
                            }
                        } else {
                            $('[name="gscm"]').closest('.col-sm-6').hide()
                        }
                        polyfill('e')
                        useKeyRelation(result.data)
                    },
                    btn1: function (index, layero) {
                        if (editable) {
                            commitx(index, false)
                        } else {
                            layer.close(index)
                        }
                    },
                    btn2: function (index, layero) {
                        if (editable) {
                            commitx(index, true)
                        } else {
                            layer.close(index)
                        }
                        return false; // 与btn1的关闭层逻辑不一致
                    }
                });
            }
        })

    }

    /*function addMx(key) {
        var table = findControl(template.template_content.controls, key);
        var idx = $('#' + key).find("tr").length;
        $('#' + key).append(`<tr>
                 <td style="text-align: center"></td>
                 <td><select class="form-control" name="fylx" control="Selector"><option></option>${asOptions(findControl(template.template_content.controls, 'item-1503317853434').config.table.children, 'item-1503317870534')}</select></td>
                 <td><input type="text" name="fsrq" class="form-control" control="Date" autocomplete="off"></td>
                 <td><input type="text" name="fyje" class="form-control" control="Number" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off"></td>
                 <td><input type="text" name="bz" class="form-control" autocomplete="off"></td>
                 <td style="text-align: center"><a href="javascript:;" onclick="rmv(this)"><i class="fa fa-remove" style="color:red"></i></a></td>
             </tr>`)
        $('#' + key).find("tr").each(function (i) {
            $(this).find("td:eq(0)").text(i + 1)
        })
        var laydate = layui.laydate;
        $('#' + key).find("tr:last").find('[control="Date"]').each(function () {
            laydate.render({
                elem: this //, type: 'datetime' // 日期:date 日期+时间:datetime
            });
        })
    }*/

    function process() {
        //var id = $.table.selectColumns("ID")[0];
        var spNo = $.table.selectColumns("SP_NO")[0];
        if (!spNo) {
            $.modal.msgWarning("尚未提交审批")
            return
        }
        wecom_process(spNo);
    }

    /*function test() {
        $.ajax({
            url: ctx + "wecom-sp/sync-dyna",
            type: 'post',
            data: 'spNo=202212120005',
            success: function(result) {
                console.log(result)
            }
        })
    }*/
    function closeFun() {
        var closeds = $.table.selectColumns("CLOSED");
        for (let i = 0; i < closeds.length; i++) {
            if (closeds[i] == 1) {
                $.modal.msgWarning("已关闭的单据不可再次关闭")
                return
            }
        }
        var statuss = $.table.selectColumns("STATUS");
        for (let i = 0; i < statuss.length; i++) {
            if (statuss[i] != 2 && statuss[i] != 4) {
                $.modal.msgWarning("已通过或已撤销状态下的单据才可关闭")
                return
            }
        }
        $.modal.confirm("确认关闭吗？", function(){
            var ids = $.table.selectColumns("ID");
            $.operate.post(ctx + "fybx/close", {ids: ids.join(',')}, function(res) {
                console.log(res)
            })
        })

    }
    function dynaPrint() {
        var id = $.table.selectColumns("ID")[0];
        var spNo = $.table.selectColumns("SP_NO")[0];
        var status = $.table.selectColumns("STATUS")[0];
        if (status != 1 && status != 2) {
            $.modal.msgError("只能打印审批中、审批通过的单据");
            return
        }
//        window.open(ctx + "wecom-sp/dyna-print?id="+id+"&spNo="+spNo+"&biz=[(${biz})]&templateId=[(${templateId})]")

        let iframe = document.getElementById("print-frame");
        if (!iframe) {
            iframe = document.createElement('IFRAME');
            iframe.id = "print-frame"
            document.body.appendChild(iframe);
            iframe.setAttribute('style', 'display:none;');
        }
        iframe.src = ctx + "wecom-sp/dyna-print?id="+id+"&spNo="+spNo+"&biz=[(${biz})]"
        iframe.onload = function() { //解决图片显示不了的问题
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
            //document.body.removeChild(iframe);
        };
    }

    function vPanelUploadProof() {
        var status = $.table.selectColumns("STATUS")[0];
        if (status != 2) {
            $.modal.msgWarning("只能核销已通过审批的单据");
            return;
        }
        var closed = $.table.selectColumns("CLOSED")[0];
        if (closed == 1) {
            $.modal.msgWarning("已关闭的数据不能核销");
            return;
        }
        var id = $.table.selectColumns("ID")[0];
        var uploadCache = {};
        layer.open({
            type: 1,
            area: ['600px', '500px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '财务核销',
            content: `
<div class="row" style="margin: 5px">
    <form id="offForm">
    <div class="col-sm-6 flex">
        <label class="flex_left" style="width:110px">实际支付金额 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <input class="form-control" id="offAmount" required oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" aria-autocomplete="none">
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:110px">核销凭证 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <input type="file" id="proof" name="proof" multiple/>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:110px">备注</label>
        <div class="flex_right">
            <textarea class="form-control" rows="5" id="offMemo"></textarea>
        </div>
    </div>
    </form>
</div>`,
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function (layero, index) {
                let option = {
                    theme: "explorer-fa5", //主题
                    language: 'zh',
                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                    //deleteUrl: ctx + "common/deleteImage",
                    uploadExtraData: {key: "proof"},   //上传id，传入后台的参数
                    deleteExtraData: {key: 'id'},
                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                    enctype: 'multipart/form-data',
                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                    initialPreviewAsData: true,
                    overwriteInitial: false,
                    //initialPreviewConfig: [
                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                    //],
                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                    maxFileCount: 0, // 0:不限制上传数
                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                    //previewClass:"uploadPreview",
                    minFileSize: 5, // 5KB
                    previewFileIcon: '<i class="fa fa-file"></i>',
                    allowedPreviewTypes: ['image'],
                    showClose: false,  //是否显示右上角叉按钮
                    showUpload: false, //是否显示下方上传按钮
                    showRemove: false, // 是否显示下方移除按钮
                    //autoReplace: true,
                    //showPreview: false,//是否显示预览(false=只剩按钮)
                    showCaption: false,//底部上传按钮左侧文本
                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                    fileActionSettings: {
                        showUpload: false,		//每个文件的上传按钮
                        showDrag: false,
                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                    },
                }
                $("[name='proof']").fileinput(option).on("filebatchselected", function (e, files) {
                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                }).on("fileuploaded", function (event, data, previewId, index) {
                    //单个上传成功事件
                    console.log("fileuploaded", event, data, previewId, index)
                    var code = data.response.code;
                    if (code !== 0) {
                        $.modal.closeLoading();
                        $.modal.alertError("上传失败：" + data.response.msg);
                        return;
                    }
                    uploadCache[previewId] = data.response.tid;
                }).on('filesuccessremove', function (event, previewId, index) {
                    //上传后删除事件
                    console.log("filesuccessremove", event, previewId, index)
                    //delete cache[previewId] //bug
                    //fileArr.splice(index, 1) //bug
                    //$(this).fileinput('clear');
                    //$('[name="' + hideName + '"]').val('')
                    var tid = uploadCache[previewId];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        console.log(result)
                    }, 'json')
                    delete uploadCache[previewId]
                });
            },
            btn1: function(index, layero) {
                if (!$.validate.form('offForm')) {
                    return;
                }
                var curTids = []
                for (const uploadCacheKey in uploadCache) {
                    curTids.push(uploadCache[uploadCacheKey])
                }
                if (curTids.length == 0) {
                    $.modal.msgError("请选择上传文件");
                    return
                }
                var offAmount = $('#offAmount').val();
                var offMemo = $('#offMemo').val().trim()
                $.modal.confirm("确定提交吗？", function(){
                    $.ajax({
                        url: ctx + "wecom-sp/fybxProof",
                        data: {id,tids:curTids.join(','),offAmount,offMemo},
                        type: 'POST',
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                layer.close(index);
                                $.modal.msgSuccess(result.msg);
                                $.table.refresh();
                            } else {
                                $.modal.msgError(result.msg);
                            }
                        }
                    });
                })
            },
            btn2: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            },
            cancel: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            }
        });
    }

    function vFybxProof(id) {
        $.ajax({
            url: ctx + "wecom-sp/vFybxProof?id="+id,
            cache: false,
            success: function(result) {
                if (result.code != 0) {
                    $.modal.msgError(result.msg);
                    return
                }
                let html = []
                for (let i = 0; i < result.data.proofs.length; i++) {
                    html.push('<img src="',result.data.proofs[i].filePath,'" style="width:70px;height:50px;margin:5px;" />')
                }
                layer.open({
                    type: 1,
                    area: ['600px', '500px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    skin: 'proofClass',
                    title: '核销明细查看',
                    content: `
<div class="row" style="margin: 5px">
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">核销人</span>
        <div class="flex_right">
            <div class="form-control">${result.data.WRITE_OFF_USER}</div>
        </div>
    </div>
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">核销时间</span>
        <div class="flex_right">
            <div class="form-control">${result.data.WRITE_OFF_TIME}</div>
        </div>
    </div>
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">实际支付金额</span>
        <div class="flex_right">
            <div class="form-control">${result.data.OFF_AMOUNT||''}</div>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">核销凭证</span>
        <div class="flex-right imgPreview">
            ${html.join('')}
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">备注</span>
        <div class="flex_right">
            <div class="form-control" style="white-space: pre-wrap!important;min-height:26px;height:auto;word-wrap: break-word!important;*white-space:normal!important;">${result.data.OFF_MEMO||''}</div>
        </div>
    </div>
</div>`,
                    btn: ['<i class="fa fa-remove"></i> 关闭'
                        /*[# th:if="${@shiroUtils.getSubject().isPermitted('sp:fybx:cancel_proof')}"]*/
                        ,'<i class="fa fa-dollar"></i> 反核销'
                        /*[/]*/],
                    // 弹层外区域关闭
                    shadeClose: true,
                    success: function(layero) {
                        layero.find('.imgPreview').viewer({
                            url: 'data-original',
                            title: false,
                            navbar: false,
                        });
                    },
                    btn1: function (index, layero) {
                        layer.close(index)
                    },
                    /*[# th:if="${@shiroUtils.getSubject().isPermitted('sp:fybx:cancel_proof')}"]*/
                    btn2: function (index, layero) {
                        $.modal.confirm('确认取消该单据核销吗？', function(){
                            $.ajax({
                                url: ctx + "wecom-sp/rejectFybxProof",
                                cache: false,
                                data: {id: id},
                                type: 'post',
                                success: function (result) {
                                    if (result.code == 0) {
                                        layer.close(index);
                                        $.modal.msgSuccess(result.msg);
                                        $.table.refresh();
                                    } else {
                                        $.modal.msgError(result.msg);
                                    }
                                }
                            })
                        });
                        return false
                    }
                    /*[/]*/
                })
            }
        })
    }

    function exportFybx() {
        $.modal.confirm("即将导出，是否继续？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON("role-form");
            $.post(ctx + "fybx/exportFybx", search, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }
    function RelatedApproval(control, setting, form) {
        let v = form[setting.field.toUpperCase()]
        let hidden = '<input type="hidden"' + (control.property.require == 1 ? ' required':'') + ' control="' + setting.control + '" name="' + setting.field + '" value="' + (v || "") + '"/>';
        if (v) {
            let tmp = [];
            let list = v.split(',')
            for (let i = 0; i < list.length; i++) {
                tmp.push('<a href="javascript:;" title="点击即删除" style="color:red" onclick="rmva(this,\''+list[i]+'\',\''+setting.field+'\')">'+list[i]+'</a> ');
            }
            tmp.push("<a href=\"javascript:;\" "+setting.field+" onclick=\"connectByj(this, '" + setting.field + "')\">追加</a>")
            tmp.push(hidden)
            return tmp.join('')
        } else {
            return "<a href=\"javascript:;\" "+setting.field+" onclick=\"connectByj(this, '" + setting.field + "')\">关联个人备用金</a>" + hidden;
        }
    }

    function connectByj(a, field) {
        // let tmp = [];
        // tmp.push('<table class="table table-bordered">')
        // tmp.push('<thead><tr><th>单号</th><th>申请金额</th><th>所属公司</th><th>待还款金额</th><th>事项说明</th></tr></thead>')
        // tmp.push('<tbody></tbody>')
        // tmp.push('</table>')
        // layer.open({
        //     type: 2,
        //     area: ['900px', '500px'],
        //     fix: false,
        //     //不固定
        //     maxmin: true,
        //     shade: 0.3,
        //     title: '选择个人备用金记录',
        //     content: ctx + "byj/index?mode=select",
        //     btn: ['确定','取消'],
        //     success: function(layero) {
        //
        //     },
        //     btn1: function (index, layero) {
        //
        //     }
        // })
        $.modal.open("选择个人备用金记录", ctx + "byj/index?mode=select",'900','700',function (index, layero) {
            var iframeWin = layero.find('iframe')[0];
            let list = iframeWin.contentWindow.selectByj();
            if (list.length == 0) {
                $.modal.msgWarning("至少选择一条备用金记录");
                return;
            }

            let exist_no = [];
            let hidden = $('[name='+field+']')
            if (hidden.val()) {
                exist_no = hidden.val().split(',')
            }
            for (let i = 0; i < list.length; i++) {
                if (exist_no.indexOf(list[i].SP_NO) >= 0) {
                    $.modal.msgWarning("已忽略重复单号：" + list[i].SP_NO);
                } else if (list[i].UN_REPAY_AMOUNT <= 0) {
                    $.modal.msgWarning("单号：" + list[i].SP_NO + "无可还款金额，已忽略");
                } else {
                    exist_no.push(list[i].SP_NO);
                    $(a).before('<a href="javascript:;" title="点击即删除" style="color:red" onclick="rmva(this,\''+list[i].SP_NO+'\',\''+field+'\')">'+list[i].SP_NO+'</a> ')
                    $(a).text("追加")
                }
            }
            hidden.val(exist_no.join(","));
            layer.close(index)
        });
    }
    function rmva(a, spNo, field) {
        let exist_no = $('[name=' +field+']').val().split(',')
        exist_no.splice(exist_no.indexOf(spNo),1)
        $('[name=' + field + ']').val(exist_no.join(','));
        $(a).remove()
        if (exist_no.length == 0) {
            $('['+field+']').text('关联个人备用金')
        }
    }


    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for (var i = 1; i < footer_td.length; i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }


    let totalSumFyje = 0;

    function clearTotal() {
        totalSumFyje = 0;
    }

    function addTotal(row) {
        totalSumFyje += row.SUM_FYJE||0;
    }

    function setTotal() {
        $("#totalSumFyje").text(totalSumFyje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
    }

    function subTotal(row) {
        totalSumFyje -= row.SUM_FYJE||0;
    }
    var to = 0;
    function getAmountCount() {
        if (to) {
            clearTimeout(to);
        }
        to = setTimeout(function(){
            var data = $.common.formToJSON("role-form");
            $.ajax({
                url: prefix + "/getCount",
                type: "post",
                dataType: "json",
                data: data,
                success: function(result) {
                    if (result.code == 0) {
                        var data = result.data;
                        $("#sumSumFyje").text((data && data.sumSumFyje||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });
        }, 200)

    }

    function moneyAdd(id){
        $.modal.open("营业外收入申请", ctx + "outBusinessMoney/moneyAdd?id="+id ,600,450);
    }

    function outBusinessJump(id){
        $.modal.openTab("营业外收入", ctx + "outBusinessMoney?fybxId="+id);
    }
</script>

</body>
</html>