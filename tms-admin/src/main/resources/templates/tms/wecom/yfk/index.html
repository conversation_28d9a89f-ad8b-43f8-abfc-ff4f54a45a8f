<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请')"/>

    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-editable-select/jquery-editable-select.min.css}" media="all" type="text/css"/>

    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .add-class .layui-layer-btn1 {
            border-color: #e38d13;
            background-color: #e38d13;
            color: #fff;
        }

        .proofClass .layui-layer-btn1 {
            border-color: #e30000;
            background-color: #e30000;
            color: #fff;
        }
        .flex {
            display: flex;
            algin-items: center;
            just-content: space-between;
            margin-bottom: 5px;
        }

        .flex_left {
            width: 140px;
            text-align: right;
            padding: 5px 10px 0 10px;
        }

        .flex_right {
            min-width: 0;
            flex: 1;
        }

        .file-drop-zone {
            height: auto !important;
            min-height: auto !important;
        }

        .dyna-form .row {
            margin-right: -10px;
        }
        div.form-control {
            border: 1px solid #aaa;
            line-height: 22px;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <input name="SP_NO" class="form-control" type="text"
                               placeholder="审批单号" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <select name="STATUS" class="form-control selectpicker" data-none-selected-text="状态">
                            <option></option>
                            <option value="0">新建</option>
                            <option value="1">审批中</option>
                            <option value="2">已通过</option>
                            <option value="3">已驳回</option>
<!--                                    <option value="20">已核销</option>-->
                        </select>
                    </div>
<!--                    <div class="col-md-2 col-sm-4">-->
<!--                        <div class="form-group">-->
<!--                            <div class="col-sm-12">-->
<!--                                <input name="KHMC" class="form-control" type="text"-->
<!--                                       placeholder="客户名称" autocomplete="off" aria-required="true">-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="col-md-2 col-sm-4">
                        <input name="userName" class="form-control" type="text"
                               placeholder="申请人" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <input name="sy" class="form-control" type="text"
                               placeholder="事由" autocomplete="off" aria-required="true">
                    </div>
                    <div class="col-md-2 col-sm-3">
                        <select name="closed" class="form-control selectpicker" data-none-selected-text="关闭状态">
                            <option value=""></option>
                            <option value="0" selected>正常</option>
                            <option value="1">未打款已关闭</option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-info" onclick="settingPanel()" shiro:hasPermission="wecom:sp:setting">
                <i class="fa fa-cog"></i> 配置
            </a>
            <a class="btn btn-primary" onclick="add()">
                <i class="fa fa-plus"></i> 新增
            </a>

            <a class="btn btn-danger single disabled" onclick="del()">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-success single disabled" onclick="view()">
                <i class="fa fa-eye"></i> 查看明细
            </a>
            <a class="btn btn-info single disabled" onclick="process()">
                <i class="fa fa-laptop"></i> 审批进度
            </a>
            <a class="btn btn-danger single disabled" shiro:hasPermission="yfk:pay" onclick="vPanelPayProof()">
                <i class="fa fa-dollar"></i> 财务付款
            </a>
            <a class="btn btn-danger multiple" onclick="closeFun()">
                <i class="fa fa-close"></i> 关闭
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
<script th:src="@{/ajax/libs/jquery-editable-select/jquery-editable-select.js}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput/plugins/piexif.min.js}"></script>
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var prefix = ctx + "yfk";
    var templateId = [[${templateId}]];
    var biz = [[${biz}]];
    //收款方式
    var receivable_method = [];
    //发票类型
    var billing_type = [];
    //公司名称
    var bala_corp = [];
    //是否开票
    var if_billing = [];
    //收款单据状态
    var vbillstatus = [];

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    var dynaColumns = [[${keyReflectList}]];
    var template = JSON.parse([[${templateJson}]]);
    var otherColumns = [[${otherColumns}]];
    var optDictList = [[${optDictList}]];
    var dictBalaCorp = [[${@dict.getTypeAll('bala_corp')}]];
    var dept = [[${@shiroUtils.sysUser.dept}]]

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()
    });

    function initTable() {
        var options = {
            url: ctx + "yfk/dyna-list?templateId=[(${templateId})]&biz=[(${biz})]",
            queryParams: queryParams,
            uniqueId: 'ID',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                clearTotal()
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            onRefresh: function (params) {
                clearTotal();
            },
            onCheck: function (row, $element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "金额：<nobr id='totalAmount'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：金额：<nobr id='sumAmount'>¥0.00</nobr>";
                    }
                },
                //{field: 'BT', title: '标题'},
                {field: 'STATUS', title: '状态', formatter: function (value, row, index) {
                    let text = "";
                    if (value == 0) {
                        text += "新建"
                    } else if (value == 1) {
                        text += "审批中"
                    } else if (value == 2) {
                        text += "已通过"//；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
                    } else if (value == 3) {
                        text += "已驳回"
                    } else if (value == 4) {
                        text += "已撤销"
                    }
                    if (row.CLOSED == 1) {
                        text += "(已关闭)";
                    }
                    return text;
                }},
                {field: 'SP_NO', title: '审批单号'},
                {field: 'TYPE_NM',title: '类型'},
                {field: 'SSGS', title: '所属公司'},
                {field:'FYLX_NM', title:'费用类型'},
                {field: 'JE', title: '金额', align:'right',formatter: function(value,row,index) {
                        return (value*1).toFixed(2);
                    }},
                {field:'PAID',title:'财务已支付',formatter: function(value,row,index) {
                        if (row.STATUS == 2) {
                            if (value == 1) {
                                return "<a href='javascript:vPayProof(\""+row.ID+"\")' title='点击查看付款凭证'>已支付 (" + row.PAID_JE + ")</a>";
                            } else {
                                return '否'
                            }
                        }
                    }},
                {field: 'SKDW', title: '收款单位'},
                {field: 'SY', title: '事由',formatter: function(value,row,index) {
                        return $.table.tooltip(value);
                    }},
                {field: 'USER_NAME', title: '申请人'},
                //{field: 'KHMC', title: '客户名称'},
                //{field: 'KHH', title: '开户行'}
            ]
        };

        $.table.init(options);
    }

    function searchx() {
        var data = {};
        //data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
        $.table.search('role-form', data);
    }
    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }
    function settingPanel() {
        showSpSettings(templateId, biz);
    }

    function del() {
        var id = $.table.selectColumns("ID")[0];
        var status = $.table.selectColumns("STATUS")[0];
        if (status != 0 && status != 3) {
            $.modal.msgWarning("只能删除新建或驳回状态下的单据");
            return;
        }
        $.modal.confirm("确认删除该记录？", function () {
            $.ajax({
                url: ctx + 'wecom-sp/del[(${biz})]/' + id,
                cache: false,
                type: 'json',
                success: function (ar) {
                    if (ar.code == 0) {
                        $.table.refresh();
                    } else {
                        $.modal.msgError(ar.msg)
                    }
                }
            })
        });
    }

    function add() {
        var tables = [] // 临时存放表格html
        var formHtml = [];
        formHtml.push(`<form id="form-add" class="form-horizontal dyna-form"><div class="panel-body" style="padding:5px 20px 0px;">`);
        formHtml.push(`<div class="row">`); // row开始
        for (var i = 0; i < otherColumns.length; i++) {
            if (otherColumns[i].h == 1) {
                formHtml.push(asHidden(otherColumns[i]))
            }
        }
        let controls = template.template_content.controls;
        for (var i = 0; i < controls.length; i++) {
            let c = controls[i];
            //console.log(c)
            if (c.property.control == 'Tips') {
                formHtml.push(asTips(c))
            } else {
                var setting = findSetting(c.property.id)
                if (setting != null) {
                    if (c.property.control == 'Table') {
                        tables.push(asAddItem(c, setting)); // 表格数据临时存放到tables
                    } else {
                        formHtml.push(asAddItem(c, setting))
                    }
                }
            }
        }


        formHtml.push(`</div>`); // row结束

        formHtml.push(...tables); // 在所有表单控件增加完毕后，增加表格控件

        formHtml.push(`</div></form>`);
        var pics = []
        var curr_cust;
        var uploading = false;
        var cache = {}
        var fileArr = []
        //window.setInterval(function (){
        //    console.log(cache)
        //    console.log(fileArr)
        //}, 5000)

        layer.open({
            type: 1,
            area: ['900px', '600px'],
            fix: false,
            skin: 'add-class',
            //不固定
            maxmin: true,
            shade: 0.3,
            zIndex: 1,
            title: '新增申请',
            content: formHtml.join(''),
            btn: ['保存', '保存并提交审批', '取消'],
            //btn: ['提交审批', '取消'],
            // 弹层外区域关闭
            shadeClose: false,
            success: function (layero) {
                let temp = []
                $('#form-add').find('[name=corp_id]').remove(); // 删除自生成字段，转成下拉框
                temp.push("<select name='corp_id' required class='form-control' onchange='$(\"[name=ssgs]\").val($(this).find(\"option:selected\").text())'><option value=''></option>")
                for (let i = 0; i < dictBalaCorp.length; i++) {
                    temp.push('<option value="',dictBalaCorp[i].dictValue,'">',dictBalaCorp[i].dictLabel,'</option>')
                }
                temp.push('</select>')
                $('#form-add').find('[name=ssgs]').after(temp.join('')).hide()
                $('#form-add').find('[name=type]').bind('change',function(){
                    $('#form-add').find('[name=fylx]').val('')
                    let type_v = $(this).val();
                    if (type_v) {
                        $('#form-add').find('[name=fylx]').prop('disabled', false);
                        $('#form-add').find('[name=fylx]').find('option').each(function(){
                            let opt_v = $(this).attr('value');
                            if (!opt_v) {
                                return true;
                            }
                            if (type_v == '0') { // 业务层面
                                $(this).css('display', (opt_v == '3' || opt_v == '4' || opt_v == '13' || opt_v == '20') ? 'block':'none');
                            } else {
                                $(this).css('display', (opt_v == '3' || opt_v == '4' || opt_v == '13' || opt_v == '20') ? 'none':'block');
                            }
                        })
                        if (type_v == '0') {
                            $('#form-add').find('[name=corp_id]').closest("[sp-id]").hide()
                            $('#form-add').find('[name="khmc"]').prop("disabled", false);
                            $('#form-add').find('[name="customer_id"]').prop("disabled", false);
                            $('#form-add').find('[name="sales_dept"]').prop("disabled", false);
                            $('#form-add').find('[name="sales_id"]').prop("disabled", false);
                        } else if (type_v == '1') {
                            $('#form-add').find('[name=corp_id]').closest("[sp-id]").show()
                            $('#form-add').find('[name="khmc"]').prop("disabled", true);
                            $('#form-add').find('[name="customer_id"]').prop("disabled", true);
                            $('#form-add').find('[name="sales_dept"]').prop("disabled", true);
                            $('#form-add').find('[name="sales_id"]').prop("disabled", true);
                        }
                    } else {
                        $('#form-add').find('[name=fylx]').prop('disabled', true);
                    }
                }).change()
                polyfill('a')
                useKeyRelation();
            },
            btn1: function (index, layero) {
                commitx(index, false)
            },
            btn2: function (index, layero) {
                commitx(index, true)
                return false; // 与btn1的关闭层逻辑不一致
            }
        });
    }

    function view () {
        var id = $.table.selectColumns("ID")[0];
        $.ajax({
            url: ctx + 'wecom-sp/dyna-dtl?id=' + id + "&biz=[(${biz})]",
            cache: false,
            success: function(result) {
                if (result.code != 0) {
                    $.modal.msgError(result.msg)
                    return;
                }
                var tables = [] // 临时存放表格html
                var formHtml = [];
                var editable = result.data.STATUS == 0 || result.data.STATUS == 3;
                formHtml.push(`<form id="form-add" class="form-horizontal dyna-form view-form"><div style="padding:5px 20px 0px;">`);
                formHtml.push(`<input type="hidden" name="id" value="${id}"/>`)
                for (var i = 0; i < otherColumns.length; i++) {
                    if (otherColumns[i].h == 1) {
                        formHtml.push(asHidden(otherColumns[i], result.data))
                    }
                }

                formHtml.push(`<div class="row">`); // row开始

                let controls = template.template_content.controls;
                for (var i = 0; i < controls.length; i++) {
                    let c = controls[i];
                    if (c.property.control == 'Tips') {
                        formHtml.push(asTips(c))
                    } else {
                        var setting = findSetting(c.property.id);
                        if (setting != null) {
                            if (c.property.control == 'Table') {
                                tables.push(editable ? asEditItem(c, setting, result.data) : asViewItem(c, setting, result.data)); // 表格数据临时存放到tables
                            } else {
                                formHtml.push(editable ? asEditItem(c, setting, result.data) : asViewItem(c, setting, result.data));
                            }
                        }
                    }
                }

                formHtml.push(`</div>`); // row结束

                formHtml.push(...tables); // 在所有表单控件增加完毕后，增加表格控件

                formHtml.push(`</div></form>`);
                layer.open({
                    type: 1,
                    area: ['900px', '600px'],
                    fix: false,
                    skin: 'add-class',
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    zIndex: 1,
                    title: '申请明细',
                    content: formHtml.join(''),
                    btn: editable ? ['保存','保存并提交审批','关闭']:['关闭'],
                    // 弹层外区域关闭
                    shadeClose: false,
                    success: function (layero) {
                        if (editable) {
                            let temp = []
                            let corpId = $('[name=corp_id]').val();
                            $('#form-add').find('[name=corp_id]').remove(); // 删除自生成字段
                            temp.push("<select name='corp_id' required class='form-control' onchange='$(\"[name=ssgs]\").val($(this).find(\"option:selected\").text())'><option value=''></option>")
                            for (let i = 0; i < dictBalaCorp.length; i++) {
                                temp.push('<option value="',dictBalaCorp[i].dictValue,'"',corpId==dictBalaCorp[i].dictValue?' selected':'','>',dictBalaCorp[i].dictLabel,'</option>')
                            }
                            temp.push('</select>')
                            $('#form-add').find('[name=ssgs]').after(temp.join('')).hide()
                            let is_init = true;
                            $('#form-add').find('[name=type]').change(function(){
                                if (is_init) {
                                    is_init = false;
                                } else {
                                    $('#form-add').find('[name=fylx]').val('')
                                }
                                let type_v = $(this).val();
                                if (type_v) {
                                    $('#form-add').find('[name=fylx]').prop('disabled', false);
                                    $('#form-add').find('[name=fylx]').find('option').each(function(){
                                        let opt_v = $(this).attr('value');
                                        if (!opt_v) {
                                            return true;
                                        }
                                        if (type_v == '0') {
                                            $(this).css('display', (opt_v == '3' || opt_v == '4' || opt_v == '13') ? 'block':'none');
                                        } else {
                                            $(this).css('display', (opt_v == '3' || opt_v == '4' || opt_v == '13') ? 'none':'block');
                                        }
                                    })
                                } else {
                                    $('#form-add').find('[name=fylx]').prop('disabled', true);
                                }
                                if (type_v == '0') {
                                    $('#form-add').find('[name=corp_id]').closest("[sp-id]").hide()
                                    $('#form-add').find('[name="khmc"]').prop("disabled", false);
                                    $('#form-add').find('[name="customer_id"]').prop("disabled", false);
                                    $('#form-add').find('[name="sales_dept"]').prop("disabled", false);
                                    $('#form-add').find('[name="sales_id"]').prop("disabled", false);
                                } else if (type_v == '1') {
                                    $('#form-add').find('[name=corp_id]').closest("[sp-id]").show()
                                    $('#form-add').find('[name="khmc"]').prop("disabled", true);
                                    $('#form-add').find('[name="customer_id"]').prop("disabled", true);
                                    $('#form-add').find('[name="sales_dept"]').prop("disabled", true);
                                    $('#form-add').find('[name="sales_id"]').prop("disabled", true);
                                }
                            }).change()
                        }
                        polyfill('e')
                        useKeyRelation(result.data)
                    },
                    btn1: function (index, layero) {
                        if (editable) {
                            commitx(index, false, function(form){
                                if ($('#form-add').find('[name=type]').val() == '1') {// 公司层面：清空客户相关
                                    form['khmc'] = '';
                                    form['customer_id'] = '';
                                    form['sales_dept'] = '';
                                    form['sales_id'] = '';
                                }
                            })
                        } else {
                            layer.close(index)
                        }
                    },
                    btn2: function (index, layero) {
                        if (editable) {
                            commitx(index, true, function(form){
                                if ($('#form-add').find('[name=type]').val() == '1') {// 公司层面：清空客户相关
                                    form['khmc'] = '';
                                    form['customer_id'] = '';
                                    form['sales_dept'] = '';
                                    form['sales_id'] = '';
                                }
                            })
                        } else {
                            layer.close(index)
                        }
                        return false; // 与btn1的关闭层逻辑不一致
                    }
                });
            }
        })

    }

    /*function addMx(key) {
        var table = findControl(template.template_content.controls, key);
        var idx = $('#' + key).find("tr").length;
        $('#' + key).append(`<tr>
                 <td style="text-align: center"></td>
                 <td><select class="form-control" name="fylx" control="Selector"><option></option>${asOptions(findControl(template.template_content.controls, 'item-1503317853434').config.table.children, 'item-1503317870534')}</select></td>
                 <td><input type="text" name="fsrq" class="form-control" control="Date" autocomplete="off"></td>
                 <td><input type="text" name="fyje" class="form-control" control="Number" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off"></td>
                 <td><input type="text" name="bz" class="form-control" autocomplete="off"></td>
                 <td style="text-align: center"><a href="javascript:;" onclick="rmv(this)"><i class="fa fa-remove" style="color:red"></i></a></td>
             </tr>`)
        $('#' + key).find("tr").each(function (i) {
            $(this).find("td:eq(0)").text(i + 1)
        })
        var laydate = layui.laydate;
        $('#' + key).find("tr:last").find('[control="Date"]').each(function () {
            laydate.render({
                elem: this //, type: 'datetime' // 日期:date 日期+时间:datetime
            });
        })
    }*/

    function process() {
        //var id = $.table.selectColumns("ID")[0];
        var spNo = $.table.selectColumns("SP_NO")[0];
        if (!spNo) {
            $.modal.msgWarning("尚未提交审批")
            return
        }
        wecom_process(spNo);
    }

    // function dynaPrint() {
    //     var id = $.table.selectColumns("ID")[0];
    //     var spNo = $.table.selectColumns("SP_NO")[0];
    //     var status = $.table.selectColumns("STATUS")[0];
    //     if (status != 1 && status != 2) {
    //         $.modal.msgError("只能打印审批中、审批通过的单据");
    //         return
    //     }
    //
    //     let iframe = document.getElementById("print-frame");
    //     if (!iframe) {
    //         iframe = document.createElement('IFRAME');
    //         iframe.id = "print-frame"
    //         document.body.appendChild(iframe);
    //         iframe.setAttribute('style', 'display:none;');
    //     }
    //     iframe.src = ctx + "wecom-sp/dyna-print?id="+id+"&spNo="+spNo+"&biz=[(${biz})]"
    //     iframe.onload = function() { //解决图片显示不了的问题
    //         iframe.contentWindow.focus();
    //         iframe.contentWindow.print();
    //         //document.body.removeChild(iframe);
    //     };
    // }

    function vPanelPayProof() {
        var status = $.table.selectColumns("STATUS")[0];
        if (status != 2) {
            $.modal.msgWarning("只能对已通过审批的单据付款");
            return;
        }
        var paid = $.table.selectColumns("PAID")[0];
        if (paid == 1) {
            $.modal.msgWarning("已支付的不可再支付");
            return;
        }
        var id = $.table.selectColumns("ID")[0];
        var uploadCache = {};
        layer.open({
            type: 1,
            area: ['600px', '500px'],
            fix: false,
            skin: '',
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '财务付款',
            content: `
<div class="row" style="margin: 5px">
    <form id="offForm">
    <div class="col-sm-6 flex">
        <label class="flex_left" style="width:110px">实际支付金额 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <div class="input-group">
                <input class="form-control" id="paidJe" required min="0" value="${$.table.selectColumns("JE")[0]}">
                <div class="input-group-addon">元</div>
            </div>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:110px">支付凭证 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <input type="file" id="proof" name="proof" multiple/>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:110px">备注</label>
        <div class="flex_right">
            <textarea class="form-control" rows="5" id="offMemo"></textarea>
        </div>
    </div>
    </form>
</div>`,
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function (layero, index) {
                let option = {
                    theme: "explorer-fa5", //主题
                    language: 'zh',
                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                    //deleteUrl: ctx + "common/deleteImage",
                    uploadExtraData: {key: "proof"},   //上传id，传入后台的参数
                    deleteExtraData: {key: 'id'},
                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                    enctype: 'multipart/form-data',
                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                    initialPreviewAsData: true,
                    overwriteInitial: false,
                    //initialPreviewConfig: [
                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                    //],
                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                    maxFileCount: 0, // 0:不限制上传数
                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                    //previewClass:"uploadPreview",
                    minFileSize: 5, // 5KB
                    previewFileIcon: '<i class="fa fa-file"></i>',
                    allowedPreviewTypes: ['image'],
                    showClose: false,  //是否显示右上角叉按钮
                    showUpload: false, //是否显示下方上传按钮
                    showRemove: false, // 是否显示下方移除按钮
                    //autoReplace: true,
                    //showPreview: false,//是否显示预览(false=只剩按钮)
                    showCaption: false,//底部上传按钮左侧文本
                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                    fileActionSettings: {
                        showUpload: false,		//每个文件的上传按钮
                        showDrag: false,
                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                    },
                }
                $("[name='proof']").fileinput(option).on("filebatchselected", function (e, files) {
                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                }).on("fileuploaded", function (event, data, previewId, index) {
                    //单个上传成功事件
                    console.log("fileuploaded", event, data, previewId, index)
                    var code = data.response.code;
                    if (code !== 0) {
                        $.modal.closeLoading();
                        $.modal.alertError("上传失败：" + data.response.msg);
                        return;
                    }
                    uploadCache[previewId] = data.response.tid;
                }).on('filesuccessremove', function (event, previewId, index) {
                    //上传后删除事件
                    console.log("filesuccessremove", event, previewId, index)
                    //delete cache[previewId] //bug
                    //fileArr.splice(index, 1) //bug
                    //$(this).fileinput('clear');
                    //$('[name="' + hideName + '"]').val('')
                    var tid = uploadCache[previewId];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        console.log(result)
                    }, 'json')
                    delete uploadCache[previewId]
                });
            },
            btn1: function(index, layero) {
                if (!$.validate.form('offForm')) {
                    return;
                }
                var curTids = []
                for (const uploadCacheKey in uploadCache) {
                    curTids.push(uploadCache[uploadCacheKey])
                }
                if (curTids.length == 0) {
                    $.modal.msgError("请选择上传文件");
                    return
                }
                var offMemo = $('#offMemo').val().trim();
                var paidJe = $('#paidJe').val();
                $.modal.confirm("确定提交吗？", function(){
                    $.ajax({
                        url: ctx + "yfk/payProof",
                        data: {id,paidJe,tids:curTids.join(','),offMemo},
                        type: 'POST',
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                layer.close(index);
                                $.modal.msgSuccess(result.msg);
                                $.table.refresh();
                            } else {
                                $.modal.msgError(result.msg);
                            }
                        }
                    });
                })
            },
            btn2: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            },
            cancel: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            }
        });
    }

    function vPayProof(id) {
        $.ajax({
            url: ctx + "yfk/vPayProof?id="+id,
            cache: false,
            success: function(result) {
                if (result.code != 0) {
                    $.modal.msgError(result.msg);
                    return
                }
                let html = []
                for (let i = 0; i < result.data.proofs.length; i++) {
                    html.push('<img src="',result.data.proofs[i].filePath,'" style="width:70px;height:50px;margin:5px;" />')
                }
                layer.open({
                    type: 1,
                    area: ['600px', '300px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    skin: 'proofClass',
                    title: '支付明细查看',
                    content: `
<div class="row" style="margin: 5px">
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:72px;line-height: 16px;">支付人</span>
        <div class="flex_right">
            <div class="form-control">${result.data.PAY_USER}</div>
        </div>
    </div>
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:72px;line-height: 16px;">支付时间</span>
        <div class="flex_right">
            <div class="form-control">${result.data.PAY_TIME}</div>
        </div>
    </div>
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:72px;line-height: 16px;">支付金额</span>
        <div class="flex_right">
            <div class="form-control">${result.data.PAID_JE}</div>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:72px;line-height: 16px;">支付凭证</span>
        <div class="flex-right imgPreview">
            ${html.join('')}
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:72px;line-height: 16px;">备注</span>
        <div class="flex_right">
            <div class="form-control" style="white-space: pre-wrap!important;min-height:26px;height:auto;word-wrap: break-word!important;*white-space:normal!important;">${result.data.PAY_MEMO||''}</div>
        </div>
    </div>
</div>`,
                    btn: ['<i class="fa fa-remove"></i> 关闭'
                        /*[# th:if="${@shiroUtils.getSubject().isPermitted('yfk:cancel_pay')}"]*/
                        ,'<i class="fa fa-dollar"></i> 撤销付款'
                        /*[/]*/],
                    // 弹层外区域关闭
                    shadeClose: true,
                    success: function(layero) {
                        layero.find('.imgPreview').viewer({
                            url: 'data-original',
                            title: false,
                            navbar: false,
                        });
                    },
                    btn1: function (index, layero) {
                        layer.close(index)
                    },
                    /*[# th:if="${@shiroUtils.getSubject().isPermitted('yfk:cancel_pay')}"]*/
                    btn2: function (index, layero) {
                        $.modal.confirm('确认取消该单据支付吗？', function(){
                            $.ajax({
                                url: ctx + "yfk/rejectPayProof",
                                cache: false,
                                data: {id: id},
                                type: 'post',
                                success: function (result) {
                                    if (result.code == 0) {
                                        layer.close(index);
                                        $.modal.msgSuccess(result.msg);
                                        $.table.refresh();
                                    } else {
                                        $.modal.msgError(result.msg);
                                    }
                                }
                            })
                        });
                        return false
                    }
                    /*[/]*/
                })
            }
        })
    }
    function closeFun() {
        var closeds = $.table.selectColumns("CLOSED");
        for (let i = 0; i < closeds.length; i++) {
            if (closeds[i] == 1) {
                $.modal.msgWarning("已关闭的单据不可再次关闭")
                return
            }
        }
        var statuss = $.table.selectColumns("STATUS");
        for (let i = 0; i < statuss.length; i++) {
            if (statuss[i] != 2) {
                $.modal.msgWarning("审核通过、未支付的单据才可关闭")
                return
            }
        }
        var paids = $.table.selectColumns("PAID");
        for (let i = 0; i < paids.length; i++) {
            if (paids[i] != 0) {
                $.modal.msgWarning("审核通过、未支付的单据才可关闭")
                return
            }
        }
        $.modal.confirm("确认关闭吗？", function(){
            var ids = $.table.selectColumns("ID");
            $.operate.post(ctx + "yfk/close", {ids: ids.join(',')}, function(res) {
                console.log(res)
            })
        })

    }

    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for (var i = 1; i < footer_td.length; i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }


    let totalAmount = 0;

    function clearTotal() {
        totalAmount = 0;
    }

    function addTotal(row) {
        totalAmount += row.JE||0;
    }

    function setTotal() {
        $("#totalAmount").text(totalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
    }

    function subTotal(row) {
        totalAmount -= row.JE||0;
    }
    var to = 0;
    function getAmountCount() {
        if (to) {
            clearTimeout(to);
        }
        to = setTimeout(function(){
            var data = $.common.formToJSON("role-form");
            $.ajax({
                url: prefix + "/getCount",
                type: "post",
                dataType: "json",
                data: data,
                success: function(result) {
                    if (result.code == 0) {
                        var data = result.data;
                        $("#sumAmount").text((data && data.sumAmount||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });
        }, 200)

    }
</script>

</body>
</html>