package com.ruoyi.tms.service.client;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.tms.domain.client.AutoDispatchConfig;
import com.ruoyi.tms.vo.client.AutoDispatchAnalyzeVO;
import com.ruoyi.tms.vo.client.AutoDispatchExportVO;
import com.ruoyi.tms.vo.client.AutoDispatchSearchVO;
import com.ruoyi.tms.vo.client.AutoDispatchVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 自动调度配置表
 *
 * <AUTHOR> zjx
 * @version : v1.0.0
 * @date : 2023-07-24 10:25
 */
public interface IAutoDispatchConfigService {
    int deleteByPrimaryKey(String id);

    int insert(AutoDispatchConfig record);

    int insertSelective(AutoDispatchConfig record);

    AutoDispatchConfig selectByPrimaryKey(String id);

    List<AutoDispatchConfig> selectByAll(AutoDispatchConfig autoDispatchConfig);

    List<AutoDispatchConfig> selectAllByCarrierId(String carrierId);

    List<AutoDispatchConfig> selectAllByCustomerId(String customerId, Integer isLatest);

    int updateByPrimaryKeySelective(AutoDispatchConfig record);

    int updateByPrimaryKey(AutoDispatchConfig record);

    int updateBatch(List<AutoDispatchConfig> list);

    /**
     * 根据客户id和类型 查询自动调度配置
     *
     * @param customerId
     * @param type
     * @return
     */
    AutoDispatchVO selectListByCustomerIdAndType(AutoDispatchSearchVO autoDispatchSearchVO);

    /**
     * 删除配置信息
     *
     * @param ids
     * @return
     */
    AjaxResult rmConfigById(String ids);

    /**
     * 删除配置信息
     *
     * @param sameIds
     * @return
     */
    AjaxResult rmConfigBySameIds(String sameIds);

    /**
     * 新添
     *
     * @param autoDispatchVO
     * @return
     */
    AjaxResult addConfig(AutoDispatchVO autoDispatchVO);

    /**
     * 删除区间
     *
     * @param sectionIds
     * @return
     */
    AjaxResult rmSectionByIds(String sectionIds);

    /**
     * 获取自动调度价格
     *
     * @param customerId           客户id     必填
     * @param deliAreaIdList       提货区id   必填
     * @param arriAreaIdList       到货区id   必填
     * @param billingMethod        计价方式    必填
     * @param carLen               车长       必填
     * @param carType              车型       必填
     * @param unitPrice            单价       必填
     * @param costAmount           总价       必填
     * @param numCount             总件数
     * @param weightCount          总重量
     * @param volumeCount          总体积
     * @param mileage              总公里数
     * @param carrierId            承运商id
     * @param arriAddrNameList     到货地址名称   必填
     * @param autoDispatchConfigId 自动调度id
     * @param goodsName            货品名称
     * @param versionId            版本id
     * @param otherFeeType         在途其他费用类型
     * @param otherFee             在途其他费用
     * @param transCode            运输方式
     * @param isRoundTrip          是否往返订单   0否  1是
     * @return Map
     */
    Map<String, Object> getPrice(String customerId, List<String> deliAreaIdList, List<String> arriAreaIdList, String billingMethod
            , String carLen, String carType, BigDecimal unitPrice, BigDecimal costAmount, Double numCount, Double weightCount
            , Double volumeCount, Double mileage, String carrierId, List<String> arriAddrNameList, String autoDispatchConfigId
            , String goodsName, String versionId, String otherFeeType, BigDecimal otherFee, String transCode, Integer isRoundTrip);


    /**
     * @param billingMethod        计价方式
     * @param id                   发货单id或者运段id
     * @param type                 类别 0发货单  1运段
     * @param autoDispatchConfigId 指定配置id
     * @param versionId            版本id
     * @return
     */
    Map<String, Object> getPriceByInvIdOrSegId(String billingMethod, String id, Integer type
            , String autoDispatchConfigId, String versionId );

    /**
     * 切换自动调度配置
     *
     * @param autoDispatchVO
     * @return
     */
    AjaxResult switchConfig(AutoDispatchVO autoDispatchVO);

    /**
     * 导入
     *
     * @param file
     * @param customerId
     * @return
     */
    AjaxResult importConfig(MultipartFile file, String customerId);

    /**
     * 设置中转站
     *
     * @param autoDispatchVO
     * @return
     */
    AjaxResult setSubsectionAddr(AutoDispatchVO autoDispatchVO);

    /**
     * 修改
     *
     * @param autoDispatchVO
     * @return
     */
    AjaxResult editConfig(AutoDispatchVO autoDispatchVO);


    /**
     * 导出
     *
     * @param autoDispatchSearchVO
     * @return
     */
    List<AutoDispatchExportVO> exportList(AutoDispatchSearchVO autoDispatchSearchVO);

    /**
     * 清空数据
     *
     * @param customerId
     * @return
     */
    AjaxResult rmAll(String customerId);

    /**
     * 修改 是否可以录入送货费
     *
     * @param autoDispatchVO
     * @return
     */
    AjaxResult changeAutoDispatchDeliFee(AutoDispatchVO autoDispatchVO);

    /**
     * 修改 是否可以录入提货费
     *
     * @param autoDispatchVO
     * @return
     */
    AjaxResult changeAutoDispatchPickUpFee(AutoDispatchVO autoDispatchVO);

    /**
     * 拷贝自动调度配置
     *
     * @param sourceCustId 源客户id
     * @param targetCustId 目标客户id
     * @param deliAid      提货区id
     * @return
     */
    AjaxResult copyConfig(String sourceCustId, String targetCustId, String deliAid);

    /**
     * 查询自动调度分析列表
     *
     * @param autoDispatchAnalyzeVO
     * @return
     */
    List<AutoDispatchAnalyzeVO> selectAnalyzeList(AutoDispatchAnalyzeVO autoDispatchAnalyzeVO);
}
